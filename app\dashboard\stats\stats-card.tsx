import { IconType } from "react-icons";
import { cn } from "@/lib/utils";
import { FiArrowDown, FiArrowUp } from "react-icons/fi";

interface StatsCardProps {
  title: string;
  value: string | number;
  icon: IconType;
  trend?: {
    value: number;
    isPositive: boolean;
    data?: number[]; // For sparkline
  };
  className?: string;
}

const SparklineChart = ({
  data = [],
  color,
}: {
  data?: number[];
  color: string;
}) => {
  if (data.length === 0) return null;

  const max = Math.max(...data);
  const min = Math.min(...data);
  const range = max - min;

  const points = data
    .map((value, index) => {
      const x = (index / (data.length - 1)) * 100;
      const y = ((value - min) / range) * 40; // 40 is the height
      return `${x},${40 - y}`;
    })
    .join(" ");

  return (
    <svg className="w-[100px] h-[40px]" preserveAspectRatio="none">
      <polyline
        points={points}
        fill="none"
        stroke={color}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const StatsCard = ({
  title,
  value,
  icon: Icon,
  trend,
  className,
}: StatsCardProps) => {
  const defaultSparklineData = [4, 7, 5, 9, 8, 6, 8, 9, 7, 8];

  return (
    <div
      className={cn(
        "p-6 bg-card rounded-lg border transition-all duration-200 hover:shadow-md",
        "relative overflow-hidden group",
        className
      )}
    >
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-3">
          <div className="p-2 rounded-lg bg-primary/10">
            <Icon className="h-5 w-5 text-primary" />
          </div>
          <p className="text-sm font-medium text-muted-foreground">{title}</p>
        </div>
        {trend && (
          <div
            className={cn(
              "px-2.5 py-1 rounded-full text-xs font-medium flex items-center gap-1",
              trend.isPositive
                ? "bg-green-500/10 text-green-500"
                : "bg-red-500/10 text-red-500"
            )}
          >
            {trend.isPositive ? (
              <FiArrowUp size={12} />
            ) : (
              <FiArrowDown size={12} />
            )}
            {Math.abs(trend.value)}%
          </div>
        )}
      </div>

      <div className="flex items-end justify-between">
        <div>
          <h3 className="text-2xl font-semibold tracking-tight">{value}</h3>
          <p className="text-xs text-muted-foreground mt-1">
            vs. previous month
          </p>
        </div>
        <div className="relative h-[40px] opacity-50 group-hover:opacity-100 transition-opacity">
          <SparklineChart
            data={trend?.data || defaultSparklineData}
            color={trend?.isPositive ? "var(--green-500)" : "var(--red-500)"}
          />
        </div>
      </div>

      {/* Decorative gradient */}
      <div
        className={cn(
          "absolute inset-x-0 bottom-0 h-1 bg-gradient-to-r",
          trend?.isPositive
            ? "from-green-500/20 via-green-500/40 to-green-500/20"
            : "from-red-500/20 via-red-500/40 to-red-500/20"
        )}
      />
    </div>
  );
};
