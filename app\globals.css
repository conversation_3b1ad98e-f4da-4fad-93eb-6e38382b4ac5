@import "tailwindcss";
@plugin "tailwind-scrollbar-hide";
@custom-variant dark (&:is(.dark *));

:root {
  --background: oklch(1.0000 0 0);
  --foreground: oklch(0.3055 0.0883 271.5744);
  --card: oklch(0.9816 0.0017 247.8390);
  --card-foreground: oklch(0.3055 0.0883 271.5744);
  --popover: oklch(1.0000 0 0);
  --popover-foreground: oklch(0.3055 0.0883 271.5744);
  --primary: oklch(0.3055 0.0883 271.5744);
  --primary-foreground: oklch(1.0000 0 0);
  --secondary: oklch(0.5518 0.2104 24.9342);
  --secondary-foreground: oklch(1.0000 0 0);
  --muted: oklch(0.9414 0.0136 277.0575);
  --muted-foreground: oklch(0.5364 0.0496 273.3687);
  --accent: oklch(0.9414 0.0136 277.0575);
  --accent-foreground: oklch(0.3055 0.0883 271.5744);
  --destructive: oklch(0.5518 0.2104 24.9342);
  --destructive-foreground: oklch(1.0000 0 0);
  --border: oklch(0.9006 0.0185 269.0757);
  --input: oklch(0.9006 0.0185 269.0757);
  --ring: oklch(0.3055 0.0883 271.5744);
  --chart-1: oklch(0.5518 0.2104 24.9342);
  --chart-2: oklch(0.3055 0.0883 271.5744);
  --chart-3: oklch(0.6122 0.2082 22.2410);
  --chart-4: oklch(0.5598 0.0781 237.9818);
  --chart-5: oklch(0.3276 0.0678 257.2723);
  --sidebar: oklch(0.9816 0.0017 247.8390);
  --sidebar-foreground: oklch(0.3055 0.0883 271.5744);
  --sidebar-primary: oklch(0.3055 0.0883 271.5744);
  --sidebar-primary-foreground: oklch(1.0000 0 0);
  --sidebar-accent: oklch(0.5518 0.2104 24.9342);
  --sidebar-accent-foreground: oklch(1.0000 0 0);
  --sidebar-border: oklch(0.9006 0.0185 269.0757);
  --sidebar-ring: oklch(0.3055 0.0883 271.5744);
  --font-sans: Inter, sans-serif;
  --font-serif: Georgia, serif;
  --font-mono: Roboto Mono, monospace;
  --radius: 0rem;
  --shadow-2xs: 0px 2px 4px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0px 2px 4px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow-md: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 2px 4px -1px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 4px 6px -1px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 8px 10px -1px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0px 2px 4px 0px hsl(0 0% 0% / 0.25);
  --tracking-normal: 0rem;
  --spacing: 0.25rem;
}

.dark {
  --background: oklch(0.1570 0.0195 274.3087);
  --foreground: oklch(0.9816 0.0017 247.8390);
  --card: oklch(0.2003 0.0443 273.9550);
  --card-foreground: oklch(0.9816 0.0017 247.8390);
  --popover: oklch(0.2003 0.0443 273.9550);
  --popover-foreground: oklch(0.9816 0.0017 247.8390);
  --primary: oklch(0.5518 0.2104 24.9342);
  --primary-foreground: oklch(1.0000 0 0);
  --secondary: oklch(0.3055 0.0883 271.5744);
  --secondary-foreground: oklch(0.9816 0.0017 247.8390);
  --muted: oklch(0.3055 0.0883 271.5744);
  --muted-foreground: oklch(0.7457 0.0304 254.7208);
  --accent: oklch(0.3055 0.0883 271.5744);
  --accent-foreground: oklch(0.9816 0.0017 247.8390);
  --destructive: oklch(0.5518 0.2104 24.9342);
  --destructive-foreground: oklch(0.9816 0.0017 247.8390);
  --border: oklch(0.3055 0.0883 271.5744);
  --input: oklch(0.3055 0.0883 271.5744);
  --ring: oklch(0.5518 0.2104 24.9342);
  --chart-1: oklch(0.5518 0.2104 24.9342);
  --chart-2: oklch(0.6122 0.2082 22.2410);
  --chart-3: oklch(0.9751 0.0184 137.7951);
  --chart-4: oklch(0.8542 0.0517 199.2886);
  --chart-5: oklch(0.5598 0.0781 237.9818);
  --sidebar: oklch(0.2003 0.0443 273.9550);
  --sidebar-foreground: oklch(0.9816 0.0017 247.8390);
  --sidebar-primary: oklch(0.5518 0.2104 24.9342);
  --sidebar-primary-foreground: oklch(1.0000 0 0);
  --sidebar-accent: oklch(0.5518 0.2104 24.9342);
  --sidebar-accent-foreground: oklch(1.0000 0 0);
  --sidebar-border: oklch(0.3055 0.0883 271.5744);
  --sidebar-ring: oklch(0.5518 0.2104 24.9342);
  --font-sans: Inter, sans-serif;
  --font-serif: Georgia, serif;
  --font-mono: Roboto Mono, monospace;
  --radius: 0rem;
  --shadow-2xs: 0px 2px 4px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0px 2px 4px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow-md: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 2px 4px -1px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 4px 6px -1px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 8px 10px -1px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0px 2px 4px 0px hsl(0 0% 0% / 0.25);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);

  --tracking-tighter: calc(var(--tracking-normal) - 0.05em);
  --tracking-tight: calc(var(--tracking-normal) - 0.025em);
  --tracking-normal: var(--tracking-normal);
  --tracking-wide: calc(var(--tracking-normal) + 0.025em);
  --tracking-wider: calc(var(--tracking-normal) + 0.05em);
  --tracking-widest: calc(var(--tracking-normal) + 0.1em);
}

body {
  letter-spacing: var(--tracking-normal);
}

body {
  letter-spacing: var(--tracking-normal);
}

body {
  letter-spacing: var(--tracking-normal);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    @apply leading-relaxed;
  }
  html {
    font-size: 0.875rem;
  }
}

@utility container {
  @apply mx-auto max-w-[1500px] p-5;
}
