"use client";
import React, { useEffect } from "react";
import "@blocknote/core/fonts/inter.css";
import {
  DefaultReactSuggestionItem,
  getDefaultReactSlashMenuItems,
  SuggestionMenuController,
  useCreateBlockNote,
} from "@blocknote/react";
import { BlockNoteView } from "@blocknote/shadcn";
import "@blocknote/shadcn/style.css";
import { BlockNoteEditor, filterSuggestionItems } from "@blocknote/core";
import { ImMagicWand } from "react-icons/im";
import { useCompletion } from "ai/react";
import { toast } from "react-toastify";
import { useTheme } from "next-themes";

interface EditorProps {
  onChange: (content: string) => void;
  initialValue: string;
  editable?: boolean;
}

const BlockEditor = ({ onChange, initialValue, editable }: EditorProps) => {
  const { theme } = useTheme();

  async function uploadFile(file: File) {
    const body = new FormData();
    body.append("file", file);

    const ret = await fetch("https://tmpfiles.org/api/v1/upload", {
      method: "POST",
      body: body,
    });
    return (await ret.json()).data.url.replace(
      "tmpfiles.org/",
      "tmpfiles.org/dl/"
    );
  }

  const editor = useCreateBlockNote({
    uploadFile,
  });

  const { complete } = useCompletion({
    id: "product-description-generator",
    api: "/api/generate",
    onResponse: (response) => {
      if (response.status === 429) {
        return;
      }
      if (response.body) {
        const reader = response.body.getReader();
        let decoder = new TextDecoder();
        reader.read().then(function processText({ done, value }) {
          if (done) {
            return;
          }
          let chunk = decoder.decode(value, { stream: true });
          editor?._tiptapEditor.commands.insertContent(chunk);
          reader.read().then(processText);
        });
      } else {
        console.error("Response body is null");
      }
    },

    streamProtocol: "text",
    onError: (e) => {
      console.error(e.message);
      toast.error(e.message);
    },
  });

  const insertMagicAi = (editor: BlockNoteEditor) => {
    const prevText = editor._tiptapEditor.state.doc.textBetween(
      Math.max(0, editor._tiptapEditor.state.selection.from - 5000),
      editor._tiptapEditor.state.selection.from - 1,
      "\n"
    );
    complete(prevText);
  };

  const insertMagicItem = (editor: BlockNoteEditor) => ({
    title: "Insert Magic Text",
    onItemClick: async () => {
      const prevText = editor._tiptapEditor.state.doc.textBetween(
        Math.max(0, editor._tiptapEditor.state.selection.from - 5000),
        editor._tiptapEditor.state.selection.from - 1,
        "\n"
      );
      insertMagicAi(editor);
    },
    aliases: ["autocomplete", "ai"],
    group: "AI",
    icon: <ImMagicWand size={18} />,
    subtext: "Continue your text with AI-generated text",
  });

  const getCustomSlashMenuItems = (
    editor: BlockNoteEditor
  ): DefaultReactSuggestionItem[] => [
    insertMagicItem(editor),
    ...getDefaultReactSlashMenuItems(editor),
  ];

  // Creates a new editor instance with some initial content.

  const handleChange = async () => {
    // Converts the editor's contents from Block objects to HTML and store to state.
    const newHTML = await editor.blocksToHTMLLossy(editor.document);
    onChange(newHTML);
  };

  useEffect(() => {
    async function loadInitialHTML() {
      const blocks = await editor.tryParseHTMLToBlocks(initialValue);
      editor.replaceBlocks(editor.document, blocks);
    }
    loadInitialHTML();
  }, [editor]);

  return (
    <div className="w-full flex flex-col items-center min-h-96">
      <div className="w-full">
        <BlockNoteView
          editor={editor}
          onChange={handleChange}
          content={initialValue}
          editable={editable}
          theme={theme === "dark" ? "dark" : "light"}
          className="min-h-96"
          slashMenu={false}
        >
          <SuggestionMenuController
            triggerCharacter={"/"}
            getItems={async (query) =>
              filterSuggestionItems(getCustomSlashMenuItems(editor), query)
            }
          />
        </BlockNoteView>
      </div>
    </div>
  );
};

export default BlockEditor;
