"use server";

import { loops, loopsEvents } from "@/lib/loops";
import { getSession } from "./user";
import { prisma } from "@/prisma/prisma";
import { pusherServer } from "@/lib/pusher";
import {
  notification_type,
  notification_priority,
} from "@/prisma/generated/client";

async function sendPusherNotification(userId: string, notification: any) {
  await pusherServer.trigger(
    userId,
    `new-notification-${userId}`,
    notification
  );
}

async function sendEmailNotification({
  email,
  data,
}: {
  email: string;
  data?: any;
}) {
  await loops.sendTransactionalEmail({
    email: email,
    transactionalId: loopsEvents.find((event) => event.id === "notification")
      ?.transactionName!,
    dataVariables: data,
  });
}

export async function getNotificationSettings() {
  const { userId } = await getSession();
  const settings = await prisma.notificationSettings.findUnique({
    where: { userId },
  });

  return settings;
}

export async function getAllNotifications() {
  const { userId } = await getSession();

  const notifications = await prisma.notification.findMany({
    where: { userId: userId },
    orderBy: { created_at: "desc" },
    take: 100,
  });

  const formattedNotifications = notifications.map((notification) => ({
    id: notification.id,
    title: notification.title,
    message: notification.message,
    priority: notification.priority,
    createdAt: notification.created_at.toISOString(),
    read: notification.read, // Use actual read status
    type: notification.type,
    actionUrl: notification.actionUrl, // Add actionUrl
  }));

  return formattedNotifications;
}

export async function markNotificationAsRead(notificationId: string) {
  const session = await getSession();

  await prisma.notification.update({
    where: {
      id: notificationId,
    },
    data: {
      read: true,
      readAt: new Date(),
    },
  });
}

export async function markAllNotificationsAsRead() {
  const session = await getSession();

  await prisma.notification.updateMany({
    where: {
      userId: session.userId,
      read: false,
    },
    data: {
      read: true,
      readAt: new Date(),
    },
  });
}

export async function deleteNotification(id: string) {
  await getSession();

  await prisma.notification.delete({
    where: {
      id,
    },
  });
}

export async function deleteOlderNotifications() {
  // Get all notifications older than 30 days

  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

  await prisma.notification.deleteMany({
    where: {
      created_at: {
        lt: thirtyDaysAgo,
      },
    },
  });
}

export async function sendNotification({
  message,
  userId,
  priority,
  title,
  type,
  data,
  actionUrl,
}: {
  title: string;
  message: string;
  type: notification_type;
  priority: notification_priority;
  userId: string;
  data?: any;
  actionUrl?: string;
}) {
  try {
    // Create notification in database
    const [notification, settings, user] = await Promise.all([
      prisma.notification.create({
        data: {
          title,
          message,
          type,
          priority,
          userId,
          actionUrl,
        },
      }),
      prisma.notificationSettings.findFirst({ where: { userId } }),
      prisma.user.findUnique({ where: { id: userId } }),
    ]);

    const sendNotifications = (): Promise<void[]> => {
      const tasks: Array<Promise<any>> = [];

      if (settings?.inApp && user) {
        tasks.push(sendPusherNotification(userId, notification));
      }

      if (settings?.email && user) {
        tasks.push(
          sendEmailNotification({
            email: user.email,
            data: data,
          })
        );
      }

      return Promise.all(tasks);
    };

    await sendNotifications();
  } catch (error: any) {
    console.error("Error sending notification:", error);
    throw new Error(`Failed to send notification: ${error?.message}`);
  }
}

export async function updateNotificationSettings({
  settings,
  userId,
}: {
  settings: {
    inApp?: boolean;
    email?: boolean;
    systemUpdates?: boolean;
    newFeature?: boolean;
    securityAlert?: boolean;
    message?: boolean;
    promotion?: boolean;
    reminder?: boolean;
  };
  userId: string;
}) {
  await prisma.notificationSettings.upsert({
    where: {
      userId: userId,
    },
    update: settings,
    create: {
      inApp: settings.inApp ?? true,
      email: settings.email ?? true,
      systemUpdates: settings.systemUpdates ?? true,
      newFeature: settings.newFeature ?? true,
      securityAlert: settings.securityAlert ?? true,
      message: settings.message ?? true,
      promotion: settings.promotion ?? true,
      reminder: settings.reminder ?? true,
      userId: userId,
    },
  });
}
