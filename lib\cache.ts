"use server";
import { redis } from "@/lib/redis";
const CACHE_TTL = 3600; // 1 hour cache by default
const CACHE_PREFIX = "starterkit:cache:";

interface CacheConfig {
  ttl?: number;
  prefix?: string;
}

export async function getCachedData<T>(
  key: string,
  fetchFn: () => Promise<T>,
  config: CacheConfig = {}
): Promise<T> {
  //
  const { ttl = CACHE_TTL, prefix = CACHE_PREFIX } = config;
  const cacheKey = `${prefix}${key}`;

  try {
    // Try to get from cache first
    const cached = await redis.get(cacheKey);
    if (cached) {
      return JSON.parse(cached) as T;
    }

    // If not in cache, fetch fresh data
    const freshData = await fetchFn();

    // Cache the fresh data
    await redis.setex(cacheKey, ttl, JSON.stringify(freshData));

    return freshData;
  } catch (error) {
    console.error("Cache operation failed:", error);
    // Fallback to direct fetch if cache fails
    return await fetchFn();
  }
}

export async function invalidateCache(
  key: string,
  prefix: string = CACHE_PREFIX
): Promise<void> {
  try {
    await redis.del(`${prefix}${key}`);
  } catch (error) {
    console.error("Cache invalidation failed:", error);
  }
}

// Helper to generate consistent cache keys
export async function generateCacheKey(
  tool: string,
  params: Record<string, any>
): Promise<string> {
  const sortedParams = Object.keys(params)
    .sort()
    .reduce((acc, key) => {
      acc[key] = params[key];
      return acc;
    }, {} as Record<string, any>);

  return `${tool}:${JSON.stringify(sortedParams)}`;
}
