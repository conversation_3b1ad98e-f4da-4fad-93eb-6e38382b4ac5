"use server";
import { headers } from "next/headers";
import { auth } from "@/lib/auth";
import { redirect } from "next/navigation";
import { prisma } from "@/prisma/prisma";
import { loops } from "@/lib/loops";
import { z } from "zod";
import { bugReportSchema } from "@/app/report-bugs/_components/bug-report-form";
import { supportFormSchema } from "@/app/support/_components/support-form";

export async function createOrg({ name }: { name: string }) {
  const headersList = await headers();

  const session = await auth.api.getSession({ headers: headersList });
  if (!session?.session || !session.user)
    throw new Error("You are not authenticated");

  const org = await auth.api.createOrganization({
    body: {
      name,
      slug: `${name.toLowerCase().replace(/\s/g, "-")}-${
        session.session.userId
      }`,
    },
    headers: headersList,
  });

  if (!org) throw new Error("Failed to create organization");

  // Set the new organization as active
  await auth.api.setActiveOrganization({
    body: {
      organizationId: org?.id,
    },
    headers: headersList,
  });
}

export async function getSession() {
  const headersList = await headers();

  const [data, member, activeOrg] = await Promise.all([
    auth.api.getSession({ headers: headersList }),
    auth.api.getActiveMember({ headers: headersList }),
    (async () => {
      const sessionData = await auth.api.getSession({ headers: headersList });
      if (!sessionData?.session.activeOrganizationId) return null;
      return auth.api.getFullOrganization({
        params: {
          id: sessionData.session.activeOrganizationId,
        },

        headers: headersList,
      });
    })(),
  ]);

  if (!data?.session) {
    redirect("/auth/sign-in");
  }

  const { session, user } = data;

  if (!session.activeOrganizationId || !activeOrg || !member) {
    redirect("/switch-org");
  }

  return {
    userId: user.id,
    org: session.activeOrganizationId,
    email: user.email,
    name: user.name,
    image: user.image,
    role: member.role,
    orgName: activeOrg.name,
    memberId: member.id,
    token: session.token,
  };
}

export async function getMyOrgs() {
  const myOrgs = await auth.api.listOrganizations({
    headers: await headers(),
  });

  return myOrgs;
}

export async function switchOrg(id: string) {
  await getSession();
  const headersList = await headers();

  await Promise.all([
    auth.api.setActiveOrganization({
      body: {
        organizationId: id,
      },
      headers: headersList,
    }),
    auth.api.setActiveOrganization({
      body: {
        organizationId: id,
      },
      headers: headersList,
    }),
  ]);

  return { success: true };
}

export async function getCurrentOrg() {
  const data = await auth.api.getFullOrganization({
    headers: await headers(),
  });
  return data;
}

export async function getOrgMembers() {
  const { org } = await getSession();

  const data = await auth.api.getFullOrganization({
    params: {
      id: org,
    },
    headers: await headers(),
  });

  return data?.members;
}

export async function updateOrganizationName(
  organizationId: string,
  name: string
) {
  try {
    await prisma.organization.update({
      where: { id: organizationId },
      data: { name },
    });

    return { success: true };
  } catch (error) {
    return { error: "Failed to update organization name" };
  }
}

export async function acceptInvitation(invitationId: string) {
  await getSession();
  const headersList = await headers();

  const data = await auth.api.acceptInvitation({
    body: {
      invitationId,
    },
    headers: headersList,
  });

  return data;
}

export async function getInvitations() {
  const headersList = await headers();

  const session = await auth.api.getSession({ headers: headersList });
  if (!session) throw new Error("Session invalid");
  const data = await prisma.invitation.findMany({
    where: {
      email: session?.user.email,
      status: {
        not: "accepted",
      },
    },
    include: {
      organization: true,
    },
  });

  return data;
}

export async function getSingleInvitation(id: string) {
  await getSession();

  const invite = await prisma.invitation.findUnique({
    where: {
      id,
    },
    include: {
      organization: true,
    },
  });
  if (invite?.status === "accepted") return null;
  return invite;
}

export async function subscribeToUpdates(active: boolean) {
  const { userId, email, name } = await getSession();

  if (active) {
    await loops.deleteContact({
      email: email,
    });
  } else {
    const [firstName, lastName] = name.split(" ");
    await loops.createContact(email, {
      firstName: firstName,
      lastName: lastName || "", // Handle case where last name might be undefined
      email: email,
      userId: userId,
    });
  }
}
