"use client";
import { useState, useEffect } from "react";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import {
  getNotificationSettings,
  updateNotificationSettings,
} from "@/actions/account/notifications";
import { useQuery } from "@tanstack/react-query";
import { toast } from "react-toastify";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

export default function NotificationSettings({ userId }: { userId: string }) {
  const { data: currentSettings, refetch } = useQuery({
    queryKey: ["notification-settings", userId],
    queryFn: async () => {
      const settings = await getNotificationSettings();
      return settings;
    },
  });

  const [settings, setSettings] = useState({
    inApp: true,
    email: true,
    systemUpdates: true,
    newFeature: true,
    securityAlert: true,
    message: true,
    promotion: true,
    reminder: true,
  });

  useEffect(() => {
    if (currentSettings) {
      setSettings({
        inApp: currentSettings.inApp ?? true,
        email: currentSettings.email ?? true,
        systemUpdates: currentSettings.systemUpdates ?? true,
        newFeature: currentSettings.newFeature ?? true,
        securityAlert: currentSettings.securityAlert ?? true,
        message: currentSettings.message ?? true,
        promotion: currentSettings.promotion ?? true,
        reminder: currentSettings.reminder ?? true,
      });
    }
  }, [currentSettings]);

  const handleToggle = async (key: string) => {
    setSettings((prev) => ({
      ...prev,
      [key]: !prev[key as keyof typeof prev],
    }));
    await toast.promise(
      updateNotificationSettings({
        // @ts-ignore
        settings: { [key]: !settings[key] },
        userId,
      }),
      {
        pending: `Updating ${key} notification setting...`,
        success: `${key} notification setting updated successfully`,
        error: `Failed to update ${key} notification setting`,
      }
    );
  };

  return (
    <div className="space-y-8">
      <Card>
        <CardHeader>
          <CardTitle>Notification Channels</CardTitle>
          <CardDescription>
            Set your cell number and WhatsApp number on your account settings.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4 pt-4">
            {Object.entries(settings)
              .filter(([key]) => ["inApp", "email"].includes(key))
              .map(([key, value]) => (
                <div key={key} className="flex items-center justify-between">
                  <Label htmlFor={key} className="capitalize">
                    {key} notifications
                  </Label>
                  <Switch
                    id={key}
                    checked={value}
                    onCheckedChange={() => handleToggle(key)}
                  />
                </div>
              ))}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Allowed updates</CardTitle>
          <CardDescription>
            Manage your notification preferences for different types of updates.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4 pt-4">
            {[
              {
                key: "systemUpdates",
                label: "System Updates",
                description:
                  "Receive notifications about important system updates and maintenance.",
              },
              {
                key: "newFeature",
                label: "New Features",
                description:
                  "Get notified when new features and functionalities are launched.",
              },
              {
                key: "securityAlert",
                label: "Security Alerts",
                description:
                  "Receive alerts for security-related events and recommendations.",
              },
              {
                key: "message",
                label: "Messages",
                description:
                  "Get notifications for new messages and communications.",
              },
              {
                key: "promotion",
                label: "Promotions",
                description:
                  "Receive updates on special offers, discounts, and promotions.",
              },
              {
                key: "reminder",
                label: "Reminders",
                description:
                  "Get reminders for important events, tasks, or deadlines.",
              },
            ].map(({ key, label, description }) => (
              <div
                key={key}
                className="flex border p-3 shadow rounded-md items-start justify-between space-x-4"
              >
                <div className="flex-grow">
                  <Label htmlFor={key} className="font-medium">
                    {label}
                  </Label>
                  <p className="text-sm mt-1 text-muted-foreground">
                    {description}
                  </p>
                </div>
                <Switch
                  id={key}
                  checked={settings[key as keyof typeof settings]}
                  onCheckedChange={() => handleToggle(key)}
                  className="flex-shrink-0 mt-1"
                />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
