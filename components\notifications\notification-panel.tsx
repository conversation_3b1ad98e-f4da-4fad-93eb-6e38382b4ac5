"use client";

import type React from "react";
import { NotificationItem } from "./notification-item";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useNotifications } from "../../context/notification-provider";
import { Badge } from "../ui/badge";

export const NotificationsPanel: React.FC = () => {
  const { notifications, markAsRead } = useNotifications();

  const markAllAsRead = () => {
    Promise.all(
      notifications
        .filter((notification) => !notification.read)
        .map(({ id }) => markAsRead(id))
    );
  };

  return (
    <div className="w-96 border bg-background text-foreground rounded-lg shadow-lg overflow-hidden">
      <div className="p-3 border-b border-border flex items-center justify-between gap-3">
        <h2 className="text-lg font-semibold">Notifications</h2>
        <Badge
          variant={"dark"}
          className="cursor-pointer"
          onClick={markAllAsRead}
        >
          Mark all as read
        </Badge>
      </div>
      <ScrollArea className="h-[32rem]">
        {notifications.length === 0 ? (
          <p className="p-6 text-center text-muted-foreground">
            No notifications
          </p>
        ) : (
          notifications.map((notification) => (
            <NotificationItem
              key={notification.id}
              {...notification}
              onMarkAsRead={markAsRead}
            />
          ))
        )}
      </ScrollArea>
    </div>
  );
};
