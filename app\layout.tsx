import "./globals.css";
import { getAllNotifications } from "@/actions/account/notifications";
import { getSession } from "@/actions/account/user";
import Appbar from "@/components/sidebar/app-bar";
import { ConfirmDialogProvider } from "@/components/ui/confirm-dialog";
import { TooltipProvider } from "@/components/ui/tooltip";
import { NotificationProvider } from "@/context/notification-provider";
import { ThemeProvider } from "@/context/theme-provider";
import { auth } from "@/lib/auth";
import getQueryClient from "@/lib/query/getQueryClient";
import ReactQueryProvider from "@/lib/query/provider";
import { HydrationBoundary, dehydrate } from "@tanstack/react-query";
import { headers } from "next/headers";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { cn } from "@/lib/utils";
import { <PERSON>eist } from "next/font/google";

const font = Geist({
  subsets: ["latin"],
  weight: ["200", "300", "400", "500", "600", "700", "800", "900"],
  display: "swap",
  adjustFontFallback: false,
});

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const data = await auth.api.getSession({
    headers: await headers(),
  });

  const session = data?.session;
  const queryClient = getQueryClient();

  // Prefetch data on the server
  await Promise.all([
    queryClient.prefetchQuery({
      queryKey: ["notifications", session?.userId],
      queryFn: async () => {
        const data = await getAllNotifications();
        return data;
      },
    }),
    queryClient.prefetchQuery({
      queryKey: ["session"],
      queryFn: async () => {
        const session = await getSession();
        return session;
      },
    }),
  ]);

  return (
    <html lang="en">
      <body className={cn(`${font.className}`, "scrollbar-thin antialiased")}>
        <ReactQueryProvider>
          <HydrationBoundary state={dehydrate(queryClient)}>
            <ThemeProvider
              defaultTheme="dark"
              attribute="class"
              disableTransitionOnChange
            >
              <TooltipProvider>
                <ConfirmDialogProvider>
                  {/* <NotificationProvider session={session}> */}
                  <Appbar>
                    {children}
                    <ToastContainer
                      position="bottom-right"
                      hideProgressBar
                      theme="dark"
                      draggable
                    />
                  </Appbar>
                  {/* </NotificationProvider> */}
                </ConfirmDialogProvider>
              </TooltipProvider>
            </ThemeProvider>
          </HydrationBoundary>
        </ReactQueryProvider>
      </body>
    </html>
  );
}
