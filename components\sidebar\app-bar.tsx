"use client";
import React, { useState, useEffect, ReactNode } from "react";
import { AppSidebar } from "./app-sidebar";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "../ui/breadcrumb";
import { Separator } from "../ui/separator";
import { SidebarInset, SidebarProvider, SidebarTrigger } from "../ui/sidebar";
import { usePathname } from "next/navigation";
import Notifications from "../notifications/Notifications";
import { useFullscreen } from "@mantine/hooks";
import { FullscreenIcon } from "lucide-react";
import { Button } from "../ui/button";
import { ModeToggle } from "../shared/mode-switcher";
import { APP_NAME } from "@/utils/config";

const Appbar = ({ children }: { children: ReactNode }) => {
  //

  const pathname = usePathname();
  const [title, setTitle] = useState("Dashboard");
  const { toggle } = useFullscreen();
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    setIsReady(true);
  }, []);

  useEffect(() => {
    //  Get the pagename from local storage every 100 milliseconds
    const interval = setInterval(() => {
      const pagename = localStorage.getItem("pageName");
      if (pagename) {
        setTitle(pagename);
      }
    }, 100);
    return () => clearInterval(interval);
  }, []);

  if (!isReady || typeof window === "undefined") return null;

  if (
    pathname.startsWith("/auth") ||
    pathname === "/" ||
    pathname === "/switch-org"
  )
    return <main className="bg-transparent">{children}</main>;

  return (
    <SidebarProvider>
      <AppSidebar variant="floating" />
      <SidebarInset>
        <div className="flex h-16 shrink-0 w-full justify-between items-center gap-2 sticky top-0 bg-background z-50">
          <header className="flex px-3 border h-16 shrink-0 w-full rounded-lg justify-between items-center gap-2 sticky top-0 md:mt-4 bg-card z-50">
            <div className="flex items-center gap-2 px-4">
              <SidebarTrigger className="-ml-1" />
              <Separator orientation="vertical" className="mr-2 h-4" />
              <Breadcrumb>
                <BreadcrumbList>
                  <BreadcrumbItem className="hidden md:block">
                    {APP_NAME}
                  </BreadcrumbItem>
                  <BreadcrumbSeparator className="hidden md:block" />
                  <BreadcrumbItem>
                    <BreadcrumbPage>{title}</BreadcrumbPage>
                  </BreadcrumbItem>
                </BreadcrumbList>
              </Breadcrumb>
            </div>
            <div className="flex items-center gap-2">
              <Button onClick={toggle} variant={"outline"}>
                <FullscreenIcon />
              </Button>
              <ModeToggle />
              {/* <Notifications /> */}
            </div>
          </header>
        </div>
        <main className="container">{children}</main>
      </SidebarInset>
    </SidebarProvider>
  );
};

export default Appbar;
