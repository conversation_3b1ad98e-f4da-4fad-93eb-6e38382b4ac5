"use client";

import type React from "react";
import {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
} from "react";
import { toast } from "react-toastify";
import {
  $Enums,
  notification_priority,
  notification_type,
} from "@/prisma/generated/client";
import { pusherClient } from "@/lib/pusher";
import {
  QueryObserverResult,
  RefetchOptions,
  useQuery,
} from "@tanstack/react-query";
import {
  getAllNotifications,
  markNotificationAsRead,
} from "@/actions/account/notifications";

type Notification = {
  id: string;
  title: string;
  message: string;
  priority: notification_priority;
  createdAt: string;
  read: boolean;
  type: notification_type;
};

type NotificationContextType = {
  notifications: Notification[];
  unreadCount: number;
  fetchNotifications: (options?: RefetchOptions) => Promise<
    QueryObserverResult<
      {
        id: string;
        title: string;
        message: string;
        priority: $Enums.notification_priority;
        createdAt: string;
        read: boolean;
        type: $Enums.notification_type;
      }[],
      Error
    >
  >;
  markAsRead: (id: string) => Promise<void>;
};

const NotificationContext = createContext<NotificationContextType | undefined>(
  undefined
);

export const NotificationProvider: React.FC<{
  children: React.ReactNode;
  session: any;
}> = ({ children, session }) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);

  const { data: allNots, refetch: fetchNotifications } = useQuery({
    queryKey: ["notifications", session?.userId],
    queryFn: async () => {
      if (!session?.userId) return [];
      const data = await getAllNotifications();
      return data;
    },
    enabled: !!session?.userId,
  });

  const markAsRead = useCallback(
    async (id: string) => {
      await markNotificationAsRead(id);
      await fetchNotifications();
    },
    [fetchNotifications]
  );

  const handleNewNotification = useCallback((data: Notification) => {
    setNotifications((prev) => [data, ...prev]);
    toast.info(data.title, { autoClose: 3000 });
  }, []);

  // Update notifications when query data changes
  useEffect(() => {
    if (allNots) {
      setNotifications(allNots);
    }
  }, [allNots]);

  // Handle Pusher subscription
  useEffect(() => {
    if (!session?.userId) return;

    const channelName = `${session.userId}`;
    const eventName = `new-notification-${session.userId}`;

    pusherClient.subscribe(channelName);
    pusherClient.bind(eventName, handleNewNotification);

    return () => {
      pusherClient.unbind(eventName, handleNewNotification);
      pusherClient.unsubscribe(channelName);
    };
  }, [session?.userId, handleNewNotification]);

  const unreadCount = notifications.filter((n) => !n.read).length;

  return (
    <NotificationContext.Provider
      value={{ notifications, unreadCount, fetchNotifications, markAsRead }}
    >
      {children}
    </NotificationContext.Provider>
  );
};

export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error(
      "useNotifications must be used within a NotificationProvider"
    );
  }
  return context;
};
