"use server";

import { getSession } from "@/actions/account/user";
import { prisma } from "@/prisma/prisma";

// Event status type
type EventStatus = 'DRAFT' | 'PUBLISHED' | 'CANCELLED' | 'COMPLETED';

// Registration status type
type RegistrationStatus = 'REGISTERED' | 'WAITLISTED' | 'CANCELLED' | 'ATTENDED';

interface CreateEventData {
  title: string;
  description: string;
  eventDate: Date;
  endDate?: Date;
  location: string;
  isVirtual: boolean;
  virtualLink?: string;
  maxAttendees?: number;
  registrationDeadline?: Date;
  tags?: string[];
  requiresApproval: boolean;
  eventType: string;
  fee?: number;
  status?: EventStatus;
}

interface UpdateEventData extends Partial<CreateEventData> {
  id: string;
}

// Create a new event
export async function createEvent(data: CreateEventData) {
  try {
    const session = await getSession();
    
    // Get organizer's profile
    const organizerProfile = await prisma.alumniProfile.findUnique({
      where: { userId: session.userId },
    });
    
    if (!organizerProfile) {
      return { success: false, error: "You must create a profile before creating events" };
    }
    
    // Validate event date
    if (new Date(data.eventDate) <= new Date()) {
      return { success: false, error: "Event date must be in the future" };
    }
    
    // Validate registration deadline
    if (data.registrationDeadline && new Date(data.registrationDeadline) >= new Date(data.eventDate)) {
      return { success: false, error: "Registration deadline must be before event date" };
    }
    
    // Create the event
    const event = await prisma.event.create({
      data: {
        title: data.title,
        description: data.description,
        eventDate: new Date(data.eventDate),
        endDate: data.endDate ? new Date(data.endDate) : null,
        location: data.location,
        isVirtual: data.isVirtual,
        virtualLink: data.virtualLink,
        maxAttendees: data.maxAttendees,
        registrationDeadline: data.registrationDeadline ? new Date(data.registrationDeadline) : null,
        tags: data.tags || [],
        requiresApproval: data.requiresApproval,
        eventType: data.eventType,
        fee: data.fee || 0,
        status: data.status || 'DRAFT',
        organizerId: organizerProfile.id,
        organizationId: session.org,
      },
      include: {
        organizer: {
          select: {
            firstName: true,
            lastName: true,
            profilePicture: true,
            user: {
              select: {
                name: true,
                image: true,
              },
            },
          },
        },
      },
    });
    
    return { success: true, event };
  } catch (error) {
    console.error("Error creating event:", error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Failed to create event" 
    };
  }
}

// Update an event
export async function updateEvent(data: UpdateEventData) {
  try {
    const session = await getSession();
    
    // Get organizer's profile
    const organizerProfile = await prisma.alumniProfile.findUnique({
      where: { userId: session.userId },
    });
    
    if (!organizerProfile) {
      return { success: false, error: "Profile not found" };
    }
    
    // Find the event
    const existingEvent = await prisma.event.findUnique({
      where: { id: data.id },
    });
    
    if (!existingEvent) {
      return { success: false, error: "Event not found" };
    }
    
    // Check if user is the organizer
    if (existingEvent.organizerId !== organizerProfile.id) {
      return { success: false, error: "You can only update events you created" };
    }
    
    // Validate event date if provided
    if (data.eventDate && new Date(data.eventDate) <= new Date()) {
      return { success: false, error: "Event date must be in the future" };
    }
    
    // Validate registration deadline if provided
    if (data.registrationDeadline && data.eventDate && 
        new Date(data.registrationDeadline) >= new Date(data.eventDate)) {
      return { success: false, error: "Registration deadline must be before event date" };
    }
    
    // Update the event
    const updatedEvent = await prisma.event.update({
      where: { id: data.id },
      data: {
        ...(data.title && { title: data.title }),
        ...(data.description && { description: data.description }),
        ...(data.eventDate && { eventDate: new Date(data.eventDate) }),
        ...(data.endDate && { endDate: new Date(data.endDate) }),
        ...(data.location && { location: data.location }),
        ...(data.isVirtual !== undefined && { isVirtual: data.isVirtual }),
        ...(data.virtualLink && { virtualLink: data.virtualLink }),
        ...(data.maxAttendees !== undefined && { maxAttendees: data.maxAttendees }),
        ...(data.registrationDeadline && { registrationDeadline: new Date(data.registrationDeadline) }),
        ...(data.tags && { tags: data.tags }),
        ...(data.requiresApproval !== undefined && { requiresApproval: data.requiresApproval }),
        ...(data.eventType && { eventType: data.eventType }),
        ...(data.fee !== undefined && { fee: data.fee }),
        ...(data.status && { status: data.status }),
        updatedAt: new Date(),
      },
      include: {
        organizer: {
          select: {
            firstName: true,
            lastName: true,
            profilePicture: true,
            user: {
              select: {
                name: true,
                image: true,
              },
            },
          },
        },
        _count: {
          select: {
            registrations: true,
          },
        },
      },
    });
    
    return { success: true, event: updatedEvent };
  } catch (error) {
    console.error("Error updating event:", error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Failed to update event" 
    };
  }
}

// Get events with filters
export async function getEvents(
  page = 1,
  limit = 20,
  filters: {
    status?: EventStatus;
    eventType?: string;
    isVirtual?: boolean;
    upcoming?: boolean;
    organizerId?: string;
    tags?: string[];
    search?: string;
  } = {}
) {
  try {
    const session = await getSession();
    
    // Build where clause
    const whereClause: any = {
      organizationId: session.org,
    };
    
    if (filters.status) {
      whereClause.status = filters.status;
    }
    
    if (filters.eventType) {
      whereClause.eventType = filters.eventType;
    }
    
    if (filters.isVirtual !== undefined) {
      whereClause.isVirtual = filters.isVirtual;
    }
    
    if (filters.upcoming) {
      whereClause.eventDate = {
        gte: new Date(),
      };
    }
    
    if (filters.organizerId) {
      whereClause.organizerId = filters.organizerId;
    }
    
    if (filters.tags && filters.tags.length > 0) {
      whereClause.tags = {
        hasSome: filters.tags,
      };
    }
    
    if (filters.search) {
      whereClause.OR = [
        {
          title: {
            contains: filters.search,
            mode: 'insensitive',
          },
        },
        {
          description: {
            contains: filters.search,
            mode: 'insensitive',
          },
        },
        {
          location: {
            contains: filters.search,
            mode: 'insensitive',
          },
        },
      ];
    }
    
    // Get events with pagination
    const [events, total] = await Promise.all([
      prisma.event.findMany({
        where: whereClause,
        include: {
          organizer: {
            select: {
              firstName: true,
              lastName: true,
              profilePicture: true,
              user: {
                select: {
                  name: true,
                  image: true,
                },
              },
            },
          },
          _count: {
            select: {
              registrations: {
                where: {
                  status: 'REGISTERED',
                },
              },
            },
          },
        },
        skip: (page - 1) * limit,
        take: limit,
        orderBy: {
          eventDate: 'asc',
        },
      }),
      prisma.event.count({
        where: whereClause,
      }),
    ]);
    
    return {
      success: true,
      events,
      pagination