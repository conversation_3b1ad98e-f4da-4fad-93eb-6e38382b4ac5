export type NotificationEvent = {
  id: string;
  title: string;
  description: string;
};

export type NotificationFeature = {
  id: string;
  name: string;
  description: string;
  events: NotificationEvent[];
};

export const notificationFeatures: NotificationFeature[] = [
  {
    id: "chat",
    name: "<PERSON>t",
    description: "Chat notifications",
    events: [
      {
        id: "message",
        title: "Message",
        description: "Message notifications",
      },
      {
        id: "typing",
        title: "Typing",
        description: "Typing notifications",
      },
      
    ],
  },
];

export const defaultNotificationPreferences = notificationFeatures.reduce(
  (acc, feature) => ({
    ...acc,
    [feature.id]: feature.events.reduce(
      (events, event) => ({
        ...events,
        [event.id]: true,
      }),
      {}
    ),
  }),
  {}
);
