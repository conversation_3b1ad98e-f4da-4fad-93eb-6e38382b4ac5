"use client";
import React from "react";
import {
  Pop<PERSON>,
  <PERSON>overContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { NotificationButton } from "./notification-button";
import { NotificationsPanel } from "./notification-panel";

const Notifications = () => {
  const [isOpen, setIsOpen] = React.useState(false);

  return (
    <Popover open={isOpen} onOpenChange={(open) => setIsOpen(open)}>
      <PopoverTrigger>
        <NotificationButton onClick={() => setIsOpen(!isOpen)} />
      </PopoverTrigger>
      <PopoverContent className="w-96 p-0 mr-4">
        <NotificationsPanel />
      </PopoverContent>
    </Popover>
  );
};

export default Notifications;
