import { betterAuth } from "better-auth";
import { prismaAdapter } from "better-auth/adapters/prisma";
import { organization } from "better-auth/plugins";
import { loops, loopsEvents } from "./loops";
import { prisma } from "@/prisma/prisma";
import { updateNotificationSettings } from "@/actions/account/notifications";
import { APP_NAME } from "@/utils/config";
import { nextCookies } from "better-auth/next-js";
import env from "./env";

export const auth = betterAuth({
  database: prismaAdapter(prisma, {
    provider: "postgresql",
  }),
  plugins: [organization(), nextCookies()],
  account: {
    accountLinking: {
      trustedProviders: ["google"],
    },
  },
  appName: APP_NAME,
  emailAndPassword: {
    async sendResetPassword({ user, token, url }) {
      await loops.sendEvent({
        eventName: loopsEvents.find((e) => e.id === "password")!
          .transactionName!,
        email: user.email,
      });
    },
    enabled: true,
    autoSignIn: true,
  },
  socialProviders: {
    google: {
      clientId: process.env.AUTH_GOOGLE_ID!,
      clientSecret: process.env.AUTH_GOOGLE_SECRET!,
    },
  },
  databaseHooks: {
    user: {
      create: {
        after: async (user) => {
          // const [firstName, lastName] = user.name.split(" ");
          // const syncLoopsUser = async () => {
          //   const contact = await loops.findContact({
          //     email: user.email,
          //   });
          //   if (!contact) {
          //     await loops.createContact(user.email, {
          //       firstName: firstName,
          //       lastName: lastName,
          //       email: user.email,
          //       userId: user.id,
          //     });
          //   } else {
          //     await loops.updateContact(user.email, {
          //       firstName: firstName,
          //       lastName: lastName,
          //       email: user.email,
          //       userId: user.id,
          //     });
          //   }
          // };
          // await Promise.all([
          //   updateNotificationSettings({
          //     settings: {
          //       inApp: true,
          //       email: true,
          //     },
          //     userId: user.id,
          //   }),
          //   syncLoopsUser(),
          // ]);
        },
      },
    },
  },
});
