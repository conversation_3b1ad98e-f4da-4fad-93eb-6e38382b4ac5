interface Env {
  DATABASE_URL: string;
  WEBHOOK_SECRET: string;
  OPENAI_API_KEY: string;
  LOOPS_API_KEY: string;
  BETTER_AUTH_SECRET: string;
  BETTER_AUTH_URL: string;
  NEXT_PUBLIC_SOKETI_HOST: string;
  NEXT_PUBLIC_SOKETI_KEY: string;
  SOKETI_SECRET: string;
  SOKETI_APP_ID: string;
  UPLOADTHING_TOKEN: string;
}

const env: Env = {
  DATABASE_URL: process.env.DATABASE_URL!,
  WEBHOOK_SECRET: process.env.WEBHOOK_SECRET!,
  OPENAI_API_KEY: process.env.OPENAI_API_KEY!,
  LOOPS_API_KEY: process.env.LOOPS_API_KEY!,
  BETTER_AUTH_SECRET: process.env.BETTER_AUTH_SECRET!,
  BETTER_AUTH_URL: process.env.BETTER_AUTH_URL!,
  NEXT_PUBLIC_SOKETI_HOST: process.env.NEXT_PUBLIC_SOKETI_HOST!,
  NEXT_PUBLIC_SOKETI_KEY: process.env.NEXT_PUBLIC_SOKETI_KEY!,
  SOKETI_SECRET: process.env.SOKETI_SECRET!,
  SOKETI_APP_ID: process.env.SOKETI_APP_ID!,
  UPLOADTHING_TOKEN: process.env.UPLOADTHING_TOKEN!,
};

export default env;
