import { getSession } from "@/actions/account/user";
import MailSubscribe from "./_components/MailSubscribe";
import getQueryClient from "@/lib/query/getQueryClient";
import { getNotificationSettings } from "@/actions/account/notifications";
import NotificationSettings from "./_components/settings-form";
import PageHeader from "@/components/shared/page-header";

export default async function SettingsPage() {
  const { userId } = await getSession();

  const client = getQueryClient();
  await client.prefetchQuery({
    queryKey: ["notification-settings", userId],
    queryFn: async () => {
      const settings = await getNotificationSettings();
      return settings;
    },
  });

  return (
    <div className="space-y-6">
      <PageHeader title="General Settings" />
      <div className="flex items-center justify-between gap-4">
        <div>
          <h3 className="text-lg font-medium">Settings Overview</h3>
          <p className="text-sm text-muted-foreground">
            Manage your user settings and features
          </p>
        </div>
      </div>
      <div className="flex flex-col gap-4">
        <MailSubscribe />
        <NotificationSettings userId={userId} />
      </div>
    </div>
  );
}
