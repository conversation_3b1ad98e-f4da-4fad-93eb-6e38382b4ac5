import Redis from "ioredis";

if (!process.env.REDIS_URL) {
  throw new Error("REDIS_URL environment variable is not defined");
}

export const redis = new Redis(process.env.REDIS_URL, {
  retryStrategy(times) {
    const delay = Math.min(times * 50, 2000);
    return delay;
  },
  maxRetriesPerRequest: 3,
  enableReadyCheck: true,
  reconnectOnError: (err) => {
    const targetError = "READONLY";
    if (err.message.includes(targetError)) {
      return true;
    }
    return false;
  },
  connectTimeout: 10000,
  lazyConnect: true,
});

redis.on("error", (error) => {
  console.error("Redis connection error:", error);
});

redis.on("connect", () => {
  console.log("Successfully connected to Redis");
});

const PRESENCE_TTL = 120; // Time in seconds before presence expires
const PRESENCE_KEY_PREFIX = "user:presence:";

export async function updateUserPresence(
  userId: string,
  organisationId: string
) {
  const key = `${PRESENCE_KEY_PREFIX}${organisationId}`;
  const now = Date.now();

  try {
    await redis.hset(key, {
      [userId]: now.toString(),
    });

    // Set TTL for the hash
    await redis.expire(key, PRESENCE_TTL);
  } catch (error) {
    console.error("Error updating user presence:", error);
    throw error;
  }
}

export async function getOnlineUsers(
  organisationId: string
): Promise<string[]> {
  const key = `${PRESENCE_KEY_PREFIX}${organisationId}`;
  try {
    const presenceData = (await redis.hgetall(key)) as Record<string, string>;
    if (!presenceData) return [];

    const now = Date.now();
    const onlineThreshold = now - PRESENCE_TTL * 1000;

    return Object.entries(presenceData)
      .filter(([_, lastSeen]) => parseInt(lastSeen) > onlineThreshold)
      .map(([userId]) => userId);
  } catch (error) {
    console.error("Error getting online users:", error);
    return [];
  }
}

export async function removeUserPresence(
  userId: string,
  organisationId: string
) {
  const key = `${PRESENCE_KEY_PREFIX}${organisationId}`;
  try {
    await redis.hdel(key, userId);
  } catch (error) {
    console.error("Error removing user presence:", error);
    throw error;
  }
}
