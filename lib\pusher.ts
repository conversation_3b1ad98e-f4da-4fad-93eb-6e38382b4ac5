import PusherServer from "pusher";
import PusherClient from "pusher-js";

export const pusherServer = new PusherServer({
  appId: process.env.SOKETI_APP_ID!,
  key: process.env.NEXT_PUBLIC_SOKETI_KEY!,
  secret: process.env.SOKETI_SECRET!,
  cluster: "",
  useTLS: true,
  host: "soketi.weevolve-ai.com",
  port: "443",
});

export const pusherClient = new PusherClient(
  process.env.NEXT_PUBLIC_SOKETI_KEY!,
  {
    cluster: "",
    wsHost: "soketi.weevolve-ai.com",
    wsPort: 6001,
    wssPort: 443,
    forceTLS: true,
    enabledTransports: ["ws", "wss"],
  }
);
