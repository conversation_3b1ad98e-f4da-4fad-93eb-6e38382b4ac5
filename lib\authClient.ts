import { createAuthClient } from "better-auth/client";
import { organizationClient } from "better-auth/client/plugins";

export const client = createAuthClient({
  plugins: [organizationClient()],
});

export const {
  changeEmail,
  changePassword,
  forgetPassword,
  deleteUser,
  linkSocial,
  listAccounts,
  listSessions,
  signOut,
  signUp,
  resetPassword,
  revokeOtherSessions,
  revokeSession,
  revokeSessions,
  sendVerificationEmail,
  signIn,
  updateUser,
  useSession,
  verifyEmail,
  unlinkAccount,
  organization,
  useActiveOrganization,
  useListOrganizations,
  useActiveMember,
  $Infer,
} = client;
