"use client";

import * as React from "react";
import {
  LayoutDashboard,
  LifeBuoy,
  MessageCircle,
  Calendar,
  Settings,
  UserCircle,
  Network,
  FileText,
  Heart,
  Newspaper,
  Search,
} from "lucide-react";
import { NavUser } from "@/components/sidebar/nav-user";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";
import NavMenu from "./nav-menu";
import Link from "next/link";
import NavHeader from "./nav-header";

type NavGroup = {
  name: string;
  url: string;
  icon: React.ElementType;
  process?: boolean;
  new?: boolean;
};

type NavData = {
  navMain: NavGroup[];
  projects: NavGroup[];
  teamManagement: NavGroup[];
  navSecondary: NavGroup[];
};

const data: NavData = {
  navMain: [
    {
      name: "Dashboard",
      url: "/dashboard",
      icon: LayoutDashboard,
    },
    {
      name: "Alumni Directory",
      url: "/directory",
      icon: Search,
    },
    {
      name: "Messages",
      url: "/messages",
      icon: MessageCircle,
    },
  ],
  projects: [
    {
      name: "My Profile",
      url: "/profile",
      icon: UserCircle,
    },
    {
      name: "My Network",
      url: "/network",
      icon: Network,
    },
    {
      name: "Alumni Posts",
      url: "/posts",
      icon: FileText,
    },
    {
      name: "Events",
      url: "/events",
      icon: Calendar,
    },
  ],
  teamManagement: [
    {
      name: "News & Updates",
      url: "/news",
      icon: Newspaper,
    },
    {
      name: "Donations",
      url: "/donations",
      icon: Heart,
    },
  ],
  navSecondary: [
    {
      name: "Settings",
      url: "/settings",
      icon: Settings,
    },
    {
      name: "Support",
      url: "/support",
      icon: LifeBuoy,
    },
  ],
};

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar variant="floating" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton size="sm" asChild>
              <NavHeader />
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <div className="grow">
          <NavMenu items={data.navMain} title="Overview" />
          <NavMenu items={data.projects} title="Alumni" />
          <NavMenu items={data.teamManagement} title="Community" />
        </div>
        <SidebarGroup {...props}>
          <SidebarGroupContent>
            <SidebarMenu>
              {data.navSecondary.map((item) => (
                <SidebarMenuItem key={item.name}>
                  <SidebarMenuButton asChild size="sm">
                    <Link href={item.url}>
                      <item.icon />
                      <span>{item.name}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
      <SidebarFooter>
        <NavUser />
      </SidebarFooter>
    </Sidebar>
  );
}
