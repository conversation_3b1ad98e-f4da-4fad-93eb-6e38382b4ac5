{"name": "starterkit", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "prisma db push && next build", "start": "next start", "lint": "next lint", "auth": "npx @better-auth/cli@latest generate"}, "dependencies": {"@ai-sdk/google": "^1.2.16", "@ai-sdk/openai": "^1.3.21", "@better-fetch/fetch": "^1.1.18", "@blocknote/core": "^0.29.1", "@blocknote/react": "^0.29.1", "@blocknote/shadcn": "^0.29.1", "@emoji-mart/data": "1.2.1", "@faker-js/faker": "^9.7.0", "@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^5.0.1", "@mantine/hooks": "^8.0.0", "@prisma/client": "^6.6.0", "@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-aspect-ratio": "^1.1.3", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-collapsible": "^1.1.4", "@radix-ui/react-context-menu": "^2.2.14", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-hover-card": "^1.1.7", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-menubar": "^1.1.7", "@radix-ui/react-navigation-menu": "^1.2.6", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slider": "^1.2.4", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toast": "^1.2.7", "@radix-ui/react-toggle": "^1.1.3", "@radix-ui/react-toggle-group": "^1.1.3", "@radix-ui/react-toolbar": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.6", "@tanstack/query-core": "^5.74.3", "@tanstack/react-query": "^5.74.3", "@tanstack/react-query-devtools": "^5.74.3", "@tanstack/react-table": "^8.21.3", "@uploadthing/react": "7.3.0", "ai": "^4.3.14", "better-auth": "1.2.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "country-data-list": "^1.4.0", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "input-otp": "^1.4.2", "ioredis": "^5.6.1", "libphonenumber-js": "^1.12.7", "loops": "^5.0.0", "lucide-react": "^0.507.0", "moment": "^2.30.1", "next": "15.3.2", "next-themes": "^0.4.6", "pusher": "^5.2.0", "pusher-js": "^8.4.0", "react": "19.1.0", "react-circle-flags": "^0.0.23", "react-day-picker": "^9.7.0", "react-dom": "19.1.0", "react-hook-form": "^7.55.0", "react-icons": "^5.5.0", "react-resizable-panels": "^3.0.1", "react-textarea-autosize": "^8.5.9", "react-toastify": "^11.0.5", "recharts": "^2.15.2", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "tailwind-merge": "^3.2.0", "tailwind-scrollbar-hide": "^2.0.0", "tailwind-variants": "^1.0.0", "tailwindcss-animate": "^1.0.7", "ua-parser-js": "^2.0.3", "uploadthing": "7.6.0", "vaul": "^1.1.2", "zod": "^3.24.4", "zod-prisma-types": "^3.2.4"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.4", "@types/node": "^22.14.1", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "eslint": "^9.24.0", "eslint-config-next": "15.3.2", "postcss": "^8.5.3", "prisma": "^6.6.0", "tailwindcss": "^4.1.4", "typescript": "^5.8.3"}}