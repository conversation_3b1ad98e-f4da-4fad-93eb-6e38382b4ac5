import React from "react";
import SwitchOrg from "./SwitchOrg";
import { getMyOrgs, getInvitations } from "@/actions/account/user";

const OrgSelection = async () => {
  const orgs = await getMyOrgs();
  const invitations = await getInvitations();

  return (
    <div className="my-10 w-full min-h-screen mx-auto px-5">
      <SwitchOrg orgs={orgs} invitations={invitations} />
    </div>
  );
};

export default OrgSelection;
