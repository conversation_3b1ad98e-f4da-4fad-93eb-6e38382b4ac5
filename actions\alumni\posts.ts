"use server";

import { getSession } from "@/actions/account/user";
import { prisma } from "@/prisma/prisma";

// Post type
type PostType = 'TEXT' | 'IMAGE' | 'VIDEO' | 'LINK' | 'POLL' | 'JOB_POSTING' | 'EVENT_SHARE';

// Post visibility type
type PostVisibility = 'PUBLIC' | 'CONNECTIONS_ONLY' | 'PRIVATE';

interface CreatePostData {
  content: string;
  postType: PostType;
  visibility: PostVisibility;
  images?: string[];
  videoUrl?: string;
  linkUrl?: string;
  linkTitle?: string;
  linkDescription?: string;
  tags?: string[];
  mentionedUserIds?: string[];
  pollOptions?: string[];
  pollExpiresAt?: Date;
  jobTitle?: string;
  jobCompany?: string;
  jobLocation?: string;
  jobType?: string;
  eventId?: string;
}

interface UpdatePostData extends Partial<CreatePostData> {
  id: string;
}

// Create a new post
export async function createPost(data: CreatePostData) {
  try {
    const session = await getSession();
    
    // Get author's profile
    const authorProfile = await prisma.alumniProfile.findUnique({
      where: { userId: session.userId },
    });
    
    if (!authorProfile) {
      return { success: false, error: "You must create a profile before creating posts" };
    }
    
    // Validate poll data
    if (data.postType === 'POLL') {
      if (!data.pollOptions || data.pollOptions.length < 2) {
        return { success: false, error: "Poll must have at least 2 options" };
      }
      if (data.pollOptions.length > 10) {
        return { success: false, error: "Poll cannot have more than 10 options" };
      }
    }
    
    // Validate job posting data
    if (data.postType === 'JOB_POSTING') {
      if (!data.jobTitle || !data.jobCompany) {
        return { success: false, error: "Job title and company are required for job postings" };
      }
    }
    
    // Create the post
    const post = await prisma.post.create({
      data: {
        content: data.content,
        postType: data.postType,
        visibility: data.visibility,
        images: data.images || [],
        videoUrl: data.videoUrl,
        linkUrl: data.linkUrl,
        linkTitle: data.linkTitle,
        linkDescription: data.linkDescription,
        tags: data.tags || [],
        mentionedUserIds: data.mentionedUserIds || [],
        pollOptions: data.pollOptions || [],
        pollExpiresAt: data.pollExpiresAt ? new Date(data.pollExpiresAt) : null,
        jobTitle: data.jobTitle,
        jobCompany: data.jobCompany,
        jobLocation: data.jobLocation,
        jobType: data.jobType,
        eventId: data.eventId,
        authorId: authorProfile.id,
        organizationId: session.org,
      },
      include: {
        author: {
          select: {
            firstName: true,
            lastName: true,
            profilePicture: true,
            currentPosition: true,
            currentCompany: true,
            user: {
              select: {
                name: true,
                image: true,
              },
            },
          },
        },
        _count: {
          select: {
            likes: true,
            comments: true,
            shares: true,
          },
        },
      },
    });
    
    return { success: true, post };
  } catch (error) {
    console.error("Error creating post:", error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Failed to create post" 
    };
  }
}

// Update a post
export async function updatePost(data: UpdatePostData) {
  try {
    const session = await getSession();
    
    // Get author's profile
    const authorProfile = await prisma.alumniProfile.findUnique({
      where: { userId: session.userId },
    });
    
    if (!authorProfile) {
      return { success: false, error: "Profile not found" };
    }
    
    // Find the post
    const existingPost = await prisma.post.findUnique({
      where: { id: data.id },
    });
    
    if (!existingPost) {
      return { success: false, error: "Post not found" };
    }
    
    // Check if user is the author
    if (existingPost.authorId !== authorProfile.id) {
      return { success: false, error: "You can only update your own posts" };
    }
    
    // Update the post
    const updatedPost = await prisma.post.update({
      where: { id: data.id },
      data: {
        ...(data.content && { content: data.content }),
        ...(data.visibility && { visibility: data.visibility }),
        ...(data.images && { images: data.images }),
        ...(data.videoUrl && { videoUrl: data.videoUrl }),
        ...(data.linkUrl && { linkUrl: data.linkUrl }),
        ...(data.linkTitle && { linkTitle: data.linkTitle }),
        ...(data.linkDescription && { linkDescription: data.linkDescription }),
        ...(data.tags && { tags: data.tags }),
        ...(data.mentionedUserIds && { mentionedUserIds: data.mentionedUserIds }),
        ...(data.jobTitle && { jobTitle: data.jobTitle }),
        ...(data.jobCompany && { jobCompany: data.jobCompany }),
        ...(data.jobLocation && { jobLocation: data.jobLocation }),
        ...(data.jobType && { jobType: data.jobType }),
        updatedAt: new Date(),
      },
      include: {
        author: {
          select: {
            firstName: true,
            lastName: true,
            profilePicture: true,
            currentPosition: true,
            currentCompany: true,
            user: {
              select: {
                name: true,
                image: true,
              },
            },
          },
        },
        _count: {
          select: {
            likes: true,
            comments: true,
            shares: true,
          },
        },
      },
    });
    
    return { success: true, post: updatedPost };
  } catch (error) {
    console.error("Error updating post:", error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Failed to update post" 
    };
  }
}

// Get news feed
export async function getNewsFeed(
  page = 1,
  limit = 20,
  filters: {
    postType?: PostType;
    authorId?: string;
    tags?: string[];
    connectionsOnly?: boolean;
  } = {}
) {
  try {
    const session = await getSession();
    
    // Get user's profile
    const userProfile = await prisma.alumniProfile.findUnique({
      where: { userId: session.userId },
      include: {
        sentConnections: {
          where: { status: 'ACCEPTED' },
          select: { recipientId: true },
        },
        receivedConnections: {
          where: { status: 'ACCEPTED' },
          select: { senderId: true },
        },
      },
    });
    
    if (!userProfile) {
      return { success: false, error: "Profile not found" };
    }
    
    // Get connected user IDs
    const connectedUserIds = [
      ...userProfile.sentConnections.map(c => c.recipientId),
      ...userProfile.receivedConnections.map(c => c.senderId),
      userProfile.id, // Include own posts
    ];
    
    // Build where clause
    const whereClause: any = {
      organizationId: session.org,
      OR: [
        {
          visibility: 'PUBLIC',
        },
        {
          visibility: 'CONNECTIONS_ONLY',
          authorId: {
            in: connectedUserIds,
          },
        },
        {
          authorId: userProfile.id, // Always show own posts
        },
      ],
    };
    
    if (filters.postType) {
      whereClause.postType = filters.postType;
    }
    
    if (filters.authorId) {
      whereClause.authorId = filters.authorId;
    }
    
    if (filters.tags && filters.tags.length > 0) {
      whereClause.tags = {
        hasSome: filters.tags,
      };
    }
    
    if (filters.connectionsOnly) {
      whereClause.authorId = {
        in: connectedUserIds.filter(id => id !== userProfile.id),
      };
    }
    
    // Get posts with pagination
    const [posts, total] = await Promise.all([
      prisma.post.findMany({
        where: whereClause,
        include: {
          author: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              profilePicture: true,
              currentPosition: true,
              currentCompany: true,
              user: {
                select: {
                  name: true,
                  image: true,
                },
              },
            },
          },
          likes: {
            select: {
              userId: true,
              user: {
                select: {
                  firstName: true,
                  lastName: true,
                  profilePicture: true,
                },
              },
            },
          },
          comments: {
            include: {
              author: {
                select: {
                  firstName: true,
                  lastName: true,
                  profilePicture: true,
                  user: {
                    select: {
                      name: true,
                      image: true,
                    },
                  },
                },
              },
              _count: {
                select: {
                  likes: true,
                },
              },
            },
            orderBy: {
              createdAt: 'asc',
            },
            take: 3, // Show only first 3 comments
          },
          pollVotes: {
            select: {
              option: true,
              userId: true,
            },
          },
          event: {
            select: {
              title: true,
              eventDate: true,
              location: true,
            },
          },
          _count: {
            select: {
              likes: true,
              comments: true,
              shares: true,
              pollVotes: true,
            },
          },
        },
        skip: (page - 1) * limit,
        take: limit,
        orderBy: {
          createdAt: 'desc',
        },
      }),
      prisma.post.count({
        where: whereClause,
      }),
    ]);
    
    // Add user interaction status to posts
    const postsWithInteractions = await Promise.all(
      posts.map(async (post) => {
        const [userLike, userPollVote] = await Promise.all([
          prisma.postLike.findUnique({
            where: {
              postId_userId: {
                postId: post.id,
                userId: userProfile.id,
              },
            },
          }),
          post.postType === 'POLL' ? prisma.pollVote.findUnique({
            where: {
              postId_userId: {
                postId: post.id,
                userId: userProfile.id,
              },
            },
          }) : null,
        ]);
        
        return {
          ...post,
          isLikedByUser: !!userLike,
          userPollVote: userPollVote?.option || null,
        };
      })
    );
    
    return {
      success: true,
      posts: postsWithInteractions,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  } catch (error) {
    console.error("Error fetching news feed:", error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Failed to fetch news feed" 
    };
  }
}

// Like/unlike a post
export async function togglePostLike(postId: string) {
  try {
    const session = await getSession();
    
    // Get user's profile
    const userProfile = await prisma.alumniProfile.findUnique({
      where: { userId: session.userId },
    });
    
    if (!userProfile) {
      return { success: false, error: "Profile not found" };
    }
    
    // Check if post exists
    const post = await prisma.post.findUnique({
      where: { id: postId },
    });
    
    if (!post) {
      return { success: false, error: "Post not found" };
    }
    
    // Check if user already liked the post
    const existingLike = await prisma.postLike.findUnique({
      where: {
        postId_userId: {
          postId,
          userId: userProfile.id,
        },
      },
    });
    
    if (existingLike) {
      // Unlike the post
      await prisma.postLike.delete({
        where: {
          postId_userId: {
            postId,
            userId: userProfile.id,
          },
        },
      });
      
      return { success: true, liked: false };
    } else {
      // Like the post
      await prisma.postLike.create({
        data: {
          postId,
          userId: userProfile.id,
          organizationId: session.org,
        },
      });
      
      return { success: true, liked: true };
    }
  } catch (error) {
    console.error("Error toggling post like:", error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Failed to toggle like" 
    };
  }
}

// Add a comment to a post
export async function addComment(postId: string, content: string, parentCommentId?: string) {
  try {
    const session = await getSession();
    
    // Get user's profile
    const userProfile = await