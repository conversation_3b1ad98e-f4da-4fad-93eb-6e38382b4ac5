"use server";

import { getSession } from "@/actions/account/user";
import { prisma } from "@/prisma/prisma";

// Post type
type PostType = 'TEXT' | 'IMAGE' | 'VIDEO' | 'LINK' | 'POLL' | 'JOB_POSTING' | 'EVENT_SHARE';

// Post visibility type
type PostVisibility = 'PUBLIC' | 'CONNECTIONS_ONLY' | 'PRIVATE';

interface CreatePostData {
  content: string;
  postType: PostType;
  visibility: PostVisibility;
  images?: string[];
  videoUrl?: string;
  linkUrl?: string;
  linkTitle?: string;
  linkDescription?: string;
  tags?: string[];
  mentionedUserIds?: string[];
  pollOptions?: string[];
  pollExpiresAt?: Date;
  jobTitle?: string;
  jobCompany?: string;
  jobLocation?: string;
  jobType?: string;
  eventId?: string;
}

interface UpdatePostData extends Partial<CreatePostData> {
  id: string;
}

// Create a new post
export async function createPost(data: CreatePostData) {
  try {
    const session = await getSession();
    
    // Get author's profile
    const authorProfile = await prisma.alumniProfile.findUnique({
      where: { userId: session.userId },
    });
    
    if (!authorProfile) {
      return { success: false, error: "You must create a profile before creating posts" };
    }
    
    // Validate poll data
    if (data.postType === 'POLL') {
      if (!data.pollOptions || data.pollOptions.length < 2) {
        return { success: false, error: "Poll must have at least 2 options" };
      }
      if (data.pollOptions.length > 10) {
        return { success: false, error: "Poll cannot have more than 10 options" };
      }
    }
    
    // Validate job posting data
    if (data.postType === 'JOB_POSTING') {
      if (!data.jobTitle || !data.jobCompany) {
        return { success: false, error: "Job title and company are required for job postings" };
      }
    }
    
    // Create the post
    const post = await prisma.post.create({
      data: {
        content: data.content,
        postType: data.postType,
        visibility: data.visibility,
        images: data.images || [],
        videoUrl: data.videoUrl,
        linkUrl: data.linkUrl,
        linkTitle: data.linkTitle,
        linkDescription: data.linkDescription,
        tags: data.tags || [],
        mentionedUserIds: data.mentionedUserIds || [],
        pollOptions: data.pollOptions || [],
        pollExpiresAt: data.pollExpiresAt ? new Date(data.pollExpiresAt) : null,
        jobTitle: data.jobTitle,
        jobCompany: data.jobCompany,
        jobLocation: data.jobLocation,
        jobType: data.jobType,
        eventId: data.eventId,
        authorId: authorProfile.id,
        organizationId: session.org,
      },
      include: {
        author: {
          select: {
            firstName: true,
            lastName: true,
            profilePicture: true,
            currentPosition: true,
            currentCompany: true,
            user: {
              select: {
                name: true,
                image: true,
              },
            },
          },
        },
        _count: {
          select: {
            likes: true,
            comments: true,
            shares: true,
          },
        },
      },
    });
    
    return { success: true, post };
  } catch (error) {
    console.error("Error creating post:", error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Failed to create post" 
    };
  }
}

// Update a post
export async function updatePost(data: UpdatePostData) {
  try {
    const session = await getSession();
    
    // Get author's profile
    const authorProfile = await prisma.alumniProfile.findUnique({
      where: { userId: session.userId },
    });
    
    if (!authorProfile) {
      return { success: false, error: "Profile not found" };
    }
    
    // Find the post
    const existingPost = await prisma.post.findUnique({
      where: { id: data.id },
    });
    
    if (!existingPost) {
      return { success: false, error: "Post not found" };
    }
    
    // Check if user is the author
    if (existingPost.authorId !== authorProfile.id) {
      return { success: false, error: "You can only update your own posts" };
    }
    
    // Update the post
    const updatedPost = await prisma.post.update({
      where: { id: data.id },
      data: {
        ...(data.content && { content: data.content }),
        ...(data.visibility && { visibility: data.visibility }),
        ...(data.images && { images: data.images }),
        ...(data.videoUrl && { videoUrl: data.videoUrl }),
        ...(data.linkUrl && { linkUrl