"use client";

import type React from "react";
import { Bell } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useNotifications } from "../../context/notification-provider";

type NotificationButtonProps = {
  onClick: () => void;
};

export const NotificationButton: React.FC<NotificationButtonProps> = ({
  onClick,
}) => {
  const { unreadCount } = useNotifications();

  return (
    <Button
      variant="outline"
      size="icon"
      className="relative"
      onClick={onClick}
      aria-label="Open notifications"
    >
      <Bell className="h-[1.2rem] w-[1.2rem]" />
      {unreadCount > 0 && (
        <span className="absolute -top-1 -right-1 h-5 w-5 rounded-full bg-red-500 text-xs text-white flex items-center justify-center">
          {unreadCount}
        </span>
      )}
    </Button>
  );
};
