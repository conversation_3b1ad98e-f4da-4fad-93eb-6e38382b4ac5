import { z } from 'zod';
import { Prisma } from '../client';

/////////////////////////////////////////
// HELPER FUNCTIONS
/////////////////////////////////////////

// DECIMAL
//------------------------------------------------------

export const DecimalJsLikeSchema: z.ZodType<Prisma.DecimalJsLike> = z.object({
  d: z.array(z.number()),
  e: z.number(),
  s: z.number(),
  toFixed: z.function(z.tuple([]), z.string()),
})

export const DECIMAL_STRING_REGEX = /^(?:-?Infinity|NaN|-?(?:0[bB][01]+(?:\.[01]+)?(?:[pP][-+]?\d+)?|0[oO][0-7]+(?:\.[0-7]+)?(?:[pP][-+]?\d+)?|0[xX][\da-fA-F]+(?:\.[\da-fA-F]+)?(?:[pP][-+]?\d+)?|(?:\d+|\d*\.\d+)(?:[eE][-+]?\d+)?))$/;

export const isValidDecimalInput =
  (v?: null | string | number | Prisma.DecimalJsLike): v is string | number | Prisma.DecimalJsLike => {
    if (v === undefined || v === null) return false;
    return (
      (typeof v === 'object' && 'd' in v && 'e' in v && 's' in v && 'toFixed' in v) ||
      (typeof v === 'string' && DECIMAL_STRING_REGEX.test(v)) ||
      typeof v === 'number'
    )
  };

/////////////////////////////////////////
// ENUMS
/////////////////////////////////////////

export const TransactionIsolationLevelSchema = z.enum(['ReadUncommitted','ReadCommitted','RepeatableRead','Serializable']);

export const UserScalarFieldEnumSchema = z.enum(['id','name','email','emailVerified','image','role','createdAt','updatedAt']);

export const SessionScalarFieldEnumSchema = z.enum(['id','expiresAt','ipAddress','userAgent','userId','activeOrganizationId','token','createdAt','updatedAt']);

export const AccountScalarFieldEnumSchema = z.enum(['id','accountId','providerId','userId','accessToken','refreshToken','idToken','expiresAt','password','accessTokenExpiresAt','refreshTokenExpiresAt','scope','createdAt','updatedAt']);

export const VerificationScalarFieldEnumSchema = z.enum(['id','identifier','value','expiresAt','createdAt','updatedAt']);

export const OrganizationScalarFieldEnumSchema = z.enum(['id','name','slug','logo','createdAt','metadata']);

export const MemberScalarFieldEnumSchema = z.enum(['id','organizationId','userId','role','createdAt']);

export const InvitationScalarFieldEnumSchema = z.enum(['id','organizationId','email','role','status','expiresAt','inviterId']);

export const PasskeyScalarFieldEnumSchema = z.enum(['id','name','publicKey','userId','webauthnUserID','counter','deviceType','backedUp','transports','createdAt','credentialID']);

export const TwoFactorScalarFieldEnumSchema = z.enum(['id','secret','backupCodes','userId']);

export const NotificationScalarFieldEnumSchema = z.enum(['id','title','message','type','priority','created_at','userId','read','readAt','actionUrl']);

export const NotificationSettingsScalarFieldEnumSchema = z.enum(['id','userId','inApp','email','systemUpdates','newFeature','securityAlert','message','promotion','reminder']);

export const AlumniProfileScalarFieldEnumSchema = z.enum(['id','userId','firstName','lastName','bio','profilePicture','graduationYear','programType','centerLocation','currentPosition','currentCompany','industry','workLocation','linkedinUrl','phoneNumber','profileVisibility','emailVisible','phoneVisible','locationVisible','offeringMentorship','seekingMentorship','skillsOffered','skillsWanted','createdAt','updatedAt']);

export const ConnectionScalarFieldEnumSchema = z.enum(['id','senderId','receiverId','status','createdAt','updatedAt']);

export const MessageScalarFieldEnumSchema = z.enum(['id','senderId','receiverId','content','isRead','readAt','createdAt']);

export const PostScalarFieldEnumSchema = z.enum(['id','authorId','title','content','type','imageUrl','createdAt','updatedAt']);

export const CommentScalarFieldEnumSchema = z.enum(['id','postId','authorId','content','createdAt','updatedAt']);

export const PostLikeScalarFieldEnumSchema = z.enum(['id','postId','userId','createdAt']);

export const EventScalarFieldEnumSchema = z.enum(['id','title','description','type','startDateTime','endDateTime','location','isVirtual','virtualUrl','maxAttendees','imageUrl','organizerInfo','createdAt','updatedAt']);

export const EventRegistrationScalarFieldEnumSchema = z.enum(['id','eventId','userId','status','createdAt','updatedAt']);

export const NewsArticleScalarFieldEnumSchema = z.enum(['id','title','content','excerpt','category','imageUrl','published','publishedAt','createdAt','updatedAt']);

export const DonationScalarFieldEnumSchema = z.enum(['id','donorId','amount','currency','type','purpose','isAnonymous','paymentMethod','transactionId','paymentStatus','createdAt','updatedAt']);

export const SortOrderSchema = z.enum(['asc','desc']);

export const NullsOrderSchema = z.enum(['first','last']);

export const UserOrderByRelevanceFieldEnumSchema = z.enum(['id','name','email','image','role']);

export const SessionOrderByRelevanceFieldEnumSchema = z.enum(['id','ipAddress','userAgent','userId','activeOrganizationId','token']);

export const AccountOrderByRelevanceFieldEnumSchema = z.enum(['id','accountId','providerId','userId','accessToken','refreshToken','idToken','password','scope']);

export const VerificationOrderByRelevanceFieldEnumSchema = z.enum(['id','identifier','value']);

export const OrganizationOrderByRelevanceFieldEnumSchema = z.enum(['id','name','slug','logo','metadata']);

export const MemberOrderByRelevanceFieldEnumSchema = z.enum(['id','organizationId','userId','role']);

export const InvitationOrderByRelevanceFieldEnumSchema = z.enum(['id','organizationId','email','role','status','inviterId']);

export const PasskeyOrderByRelevanceFieldEnumSchema = z.enum(['id','name','publicKey','userId','webauthnUserID','deviceType','transports','credentialID']);

export const TwoFactorOrderByRelevanceFieldEnumSchema = z.enum(['id','secret','backupCodes','userId']);

export const NotificationOrderByRelevanceFieldEnumSchema = z.enum(['id','title','message','userId','actionUrl']);

export const NotificationSettingsOrderByRelevanceFieldEnumSchema = z.enum(['id','userId']);

export const AlumniProfileOrderByRelevanceFieldEnumSchema = z.enum(['id','userId','firstName','lastName','bio','profilePicture','centerLocation','currentPosition','currentCompany','industry','workLocation','linkedinUrl','phoneNumber','skillsOffered','skillsWanted']);

export const ConnectionOrderByRelevanceFieldEnumSchema = z.enum(['id','senderId','receiverId']);

export const MessageOrderByRelevanceFieldEnumSchema = z.enum(['id','senderId','receiverId','content']);

export const PostOrderByRelevanceFieldEnumSchema = z.enum(['id','authorId','title','content','imageUrl']);

export const CommentOrderByRelevanceFieldEnumSchema = z.enum(['id','postId','authorId','content']);

export const PostLikeOrderByRelevanceFieldEnumSchema = z.enum(['id','postId','userId']);

export const EventOrderByRelevanceFieldEnumSchema = z.enum(['id','title','description','location','virtualUrl','imageUrl','organizerInfo']);

export const EventRegistrationOrderByRelevanceFieldEnumSchema = z.enum(['id','eventId','userId']);

export const NewsArticleOrderByRelevanceFieldEnumSchema = z.enum(['id','title','content','excerpt','imageUrl']);

export const DonationOrderByRelevanceFieldEnumSchema = z.enum(['id','donorId','currency','purpose','paymentMethod','transactionId']);

export const notification_typeSchema = z.enum(['account','system_update','new_feature','security_alert','message','promotion','reminder']);

export type notification_typeType = `${z.infer<typeof notification_typeSchema>}`

export const notification_prioritySchema = z.enum(['low','medium','high','urgent']);

export type notification_priorityType = `${z.infer<typeof notification_prioritySchema>}`

export const ProgramTypeSchema = z.enum(['ENGINEERING','BUSINESS','SCIENCE','TECHNOLOGY','OTHER']);

export type ProgramTypeType = `${z.infer<typeof ProgramTypeSchema>}`

export const ProfileVisibilitySchema = z.enum(['PUBLIC','ALUMNI_ONLY','PRIVATE']);

export type ProfileVisibilityType = `${z.infer<typeof ProfileVisibilitySchema>}`

export const ConnectionStatusSchema = z.enum(['PENDING','ACCEPTED','DECLINED','BLOCKED']);

export type ConnectionStatusType = `${z.infer<typeof ConnectionStatusSchema>}`

export const PostTypeSchema = z.enum(['GENERAL','SUCCESS_STORY','JOB_OPPORTUNITY','MENTORSHIP','ANNOUNCEMENT']);

export type PostTypeType = `${z.infer<typeof PostTypeSchema>}`

export const EventTypeSchema = z.enum(['WORKSHOP','NETWORKING','CONFERENCE','WEBINAR','SOCIAL','FUNDRAISING','MENTORSHIP']);

export type EventTypeType = `${z.infer<typeof EventTypeSchema>}`

export const RegistrationStatusSchema = z.enum(['REGISTERED','ATTENDED','NO_SHOW','CANCELLED']);

export type RegistrationStatusType = `${z.infer<typeof RegistrationStatusSchema>}`

export const NewsCategorySchema = z.enum(['GENERAL','EVENTS','SUCCESS_STORIES','OPPORTUNITIES','ANNOUNCEMENTS']);

export type NewsCategoryType = `${z.infer<typeof NewsCategorySchema>}`

export const DonationTypeSchema = z.enum(['ONE_TIME','MONTHLY','QUARTERLY','ANNUAL']);

export type DonationTypeType = `${z.infer<typeof DonationTypeSchema>}`

export const PaymentStatusSchema = z.enum(['PENDING','COMPLETED','FAILED','REFUNDED','CANCELLED']);

export type PaymentStatusType = `${z.infer<typeof PaymentStatusSchema>}`

/////////////////////////////////////////
// MODELS
/////////////////////////////////////////

/////////////////////////////////////////
// USER SCHEMA
/////////////////////////////////////////

export const UserSchema = z.object({
  id: z.string(),
  name: z.string(),
  email: z.string(),
  emailVerified: z.boolean(),
  image: z.string().nullish(),
  role: z.string().nullish(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type User = z.infer<typeof UserSchema>

// USER OPTIONAL DEFAULTS SCHEMA
//------------------------------------------------------

export const UserOptionalDefaultsSchema = UserSchema.merge(z.object({
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
}))

export type UserOptionalDefaults = z.infer<typeof UserOptionalDefaultsSchema>

/////////////////////////////////////////
// SESSION SCHEMA
/////////////////////////////////////////

export const SessionSchema = z.object({
  id: z.string(),
  expiresAt: z.coerce.date(),
  ipAddress: z.string().nullish(),
  userAgent: z.string().nullish(),
  userId: z.string().nullish(),
  activeOrganizationId: z.string().nullish(),
  token: z.string(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type Session = z.infer<typeof SessionSchema>

// SESSION OPTIONAL DEFAULTS SCHEMA
//------------------------------------------------------

export const SessionOptionalDefaultsSchema = SessionSchema.merge(z.object({
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
}))

export type SessionOptionalDefaults = z.infer<typeof SessionOptionalDefaultsSchema>

/////////////////////////////////////////
// ACCOUNT SCHEMA
/////////////////////////////////////////

export const AccountSchema = z.object({
  id: z.string(),
  accountId: z.string(),
  providerId: z.string(),
  userId: z.string(),
  accessToken: z.string().nullish(),
  refreshToken: z.string().nullish(),
  idToken: z.string().nullish(),
  expiresAt: z.coerce.date().nullish(),
  password: z.string().nullish(),
  accessTokenExpiresAt: z.coerce.date().nullish(),
  refreshTokenExpiresAt: z.coerce.date().nullish(),
  scope: z.string().nullish(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type Account = z.infer<typeof AccountSchema>

// ACCOUNT OPTIONAL DEFAULTS SCHEMA
//------------------------------------------------------

export const AccountOptionalDefaultsSchema = AccountSchema.merge(z.object({
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
}))

export type AccountOptionalDefaults = z.infer<typeof AccountOptionalDefaultsSchema>

/////////////////////////////////////////
// VERIFICATION SCHEMA
/////////////////////////////////////////

export const VerificationSchema = z.object({
  id: z.string(),
  identifier: z.string(),
  value: z.string(),
  expiresAt: z.coerce.date(),
  createdAt: z.coerce.date().nullish(),
  updatedAt: z.coerce.date().nullish(),
})

export type Verification = z.infer<typeof VerificationSchema>

// VERIFICATION OPTIONAL DEFAULTS SCHEMA
//------------------------------------------------------

export const VerificationOptionalDefaultsSchema = VerificationSchema.merge(z.object({
}))

export type VerificationOptionalDefaults = z.infer<typeof VerificationOptionalDefaultsSchema>

/////////////////////////////////////////
// ORGANIZATION SCHEMA
/////////////////////////////////////////

export const OrganizationSchema = z.object({
  id: z.string().uuid(),
  name: z.string(),
  slug: z.string().nullish(),
  logo: z.string().nullish(),
  createdAt: z.coerce.date(),
  metadata: z.string().nullish(),
})

export type Organization = z.infer<typeof OrganizationSchema>

// ORGANIZATION OPTIONAL DEFAULTS SCHEMA
//------------------------------------------------------

export const OrganizationOptionalDefaultsSchema = OrganizationSchema.merge(z.object({
  id: z.string().uuid().optional(),
  createdAt: z.coerce.date().optional(),
}))

export type OrganizationOptionalDefaults = z.infer<typeof OrganizationOptionalDefaultsSchema>

/////////////////////////////////////////
// MEMBER SCHEMA
/////////////////////////////////////////

export const MemberSchema = z.object({
  id: z.string().uuid(),
  organizationId: z.string(),
  userId: z.string().nullish(),
  role: z.string(),
  createdAt: z.coerce.date(),
})

export type Member = z.infer<typeof MemberSchema>

// MEMBER OPTIONAL DEFAULTS SCHEMA
//------------------------------------------------------

export const MemberOptionalDefaultsSchema = MemberSchema.merge(z.object({
  id: z.string().uuid().optional(),
}))

export type MemberOptionalDefaults = z.infer<typeof MemberOptionalDefaultsSchema>

/////////////////////////////////////////
// INVITATION SCHEMA
/////////////////////////////////////////

export const InvitationSchema = z.object({
  id: z.string(),
  organizationId: z.string(),
  email: z.string(),
  role: z.string().nullish(),
  status: z.string(),
  expiresAt: z.coerce.date(),
  inviterId: z.string().nullish(),
})

export type Invitation = z.infer<typeof InvitationSchema>

// INVITATION OPTIONAL DEFAULTS SCHEMA
//------------------------------------------------------

export const InvitationOptionalDefaultsSchema = InvitationSchema.merge(z.object({
}))

export type InvitationOptionalDefaults = z.infer<typeof InvitationOptionalDefaultsSchema>

/////////////////////////////////////////
// PASSKEY SCHEMA
/////////////////////////////////////////

export const PasskeySchema = z.object({
  id: z.string(),
  name: z.string().nullish(),
  publicKey: z.string(),
  userId: z.string().nullish(),
  webauthnUserID: z.string(),
  counter: z.number().int(),
  deviceType: z.string(),
  backedUp: z.boolean(),
  transports: z.string().nullish(),
  createdAt: z.coerce.date().nullish(),
  credentialID: z.string(),
})

export type Passkey = z.infer<typeof PasskeySchema>

// PASSKEY OPTIONAL DEFAULTS SCHEMA
//------------------------------------------------------

export const PasskeyOptionalDefaultsSchema = PasskeySchema.merge(z.object({
}))

export type PasskeyOptionalDefaults = z.infer<typeof PasskeyOptionalDefaultsSchema>

/////////////////////////////////////////
// TWO FACTOR SCHEMA
/////////////////////////////////////////

export const TwoFactorSchema = z.object({
  id: z.string(),
  secret: z.string(),
  backupCodes: z.string(),
  userId: z.string().nullish(),
})

export type TwoFactor = z.infer<typeof TwoFactorSchema>

// TWO FACTOR OPTIONAL DEFAULTS SCHEMA
//------------------------------------------------------

export const TwoFactorOptionalDefaultsSchema = TwoFactorSchema.merge(z.object({
}))

export type TwoFactorOptionalDefaults = z.infer<typeof TwoFactorOptionalDefaultsSchema>

/////////////////////////////////////////
// NOTIFICATION SCHEMA
/////////////////////////////////////////

export const NotificationSchema = z.object({
  type: notification_typeSchema,
  priority: notification_prioritySchema,
  id: z.string().uuid(),
  title: z.string(),
  message: z.string(),
  created_at: z.coerce.date(),
  userId: z.string(),
  read: z.boolean(),
  readAt: z.coerce.date().nullish(),
  actionUrl: z.string().nullish(),
})

export type Notification = z.infer<typeof NotificationSchema>

// NOTIFICATION OPTIONAL DEFAULTS SCHEMA
//------------------------------------------------------

export const NotificationOptionalDefaultsSchema = NotificationSchema.merge(z.object({
  priority: notification_prioritySchema.optional(),
  id: z.string().uuid().optional(),
  created_at: z.coerce.date().optional(),
  read: z.boolean().optional(),
}))

export type NotificationOptionalDefaults = z.infer<typeof NotificationOptionalDefaultsSchema>

/////////////////////////////////////////
// NOTIFICATION SETTINGS SCHEMA
/////////////////////////////////////////

export const NotificationSettingsSchema = z.object({
  id: z.string().cuid(),
  userId: z.string(),
  inApp: z.boolean(),
  email: z.boolean(),
  systemUpdates: z.boolean(),
  newFeature: z.boolean(),
  securityAlert: z.boolean(),
  message: z.boolean(),
  promotion: z.boolean(),
  reminder: z.boolean(),
})

export type NotificationSettings = z.infer<typeof NotificationSettingsSchema>

// NOTIFICATION SETTINGS OPTIONAL DEFAULTS SCHEMA
//------------------------------------------------------

export const NotificationSettingsOptionalDefaultsSchema = NotificationSettingsSchema.merge(z.object({
  id: z.string().cuid().optional(),
  inApp: z.boolean().optional(),
  email: z.boolean().optional(),
  systemUpdates: z.boolean().optional(),
  newFeature: z.boolean().optional(),
  securityAlert: z.boolean().optional(),
  message: z.boolean().optional(),
  promotion: z.boolean().optional(),
  reminder: z.boolean().optional(),
}))

export type NotificationSettingsOptionalDefaults = z.infer<typeof NotificationSettingsOptionalDefaultsSchema>

/////////////////////////////////////////
// ALUMNI PROFILE SCHEMA
/////////////////////////////////////////

export const AlumniProfileSchema = z.object({
  programType: ProgramTypeSchema,
  profileVisibility: ProfileVisibilitySchema,
  id: z.string().uuid(),
  userId: z.string(),
  firstName: z.string(),
  lastName: z.string(),
  bio: z.string().nullish(),
  profilePicture: z.string().nullish(),
  graduationYear: z.number().int(),
  centerLocation: z.string(),
  currentPosition: z.string().nullish(),
  currentCompany: z.string().nullish(),
  industry: z.string().nullish(),
  workLocation: z.string().nullish(),
  linkedinUrl: z.string().nullish(),
  phoneNumber: z.string().nullish(),
  emailVisible: z.boolean(),
  phoneVisible: z.boolean(),
  locationVisible: z.boolean(),
  offeringMentorship: z.boolean(),
  seekingMentorship: z.boolean(),
  skillsOffered: z.string().nullish(),
  skillsWanted: z.string().nullish(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type AlumniProfile = z.infer<typeof AlumniProfileSchema>

// ALUMNI PROFILE OPTIONAL DEFAULTS SCHEMA
//------------------------------------------------------

export const AlumniProfileOptionalDefaultsSchema = AlumniProfileSchema.merge(z.object({
  profileVisibility: ProfileVisibilitySchema.optional(),
  id: z.string().uuid().optional(),
  emailVisible: z.boolean().optional(),
  phoneVisible: z.boolean().optional(),
  locationVisible: z.boolean().optional(),
  offeringMentorship: z.boolean().optional(),
  seekingMentorship: z.boolean().optional(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
}))

export type AlumniProfileOptionalDefaults = z.infer<typeof AlumniProfileOptionalDefaultsSchema>

/////////////////////////////////////////
// CONNECTION SCHEMA
/////////////////////////////////////////

export const ConnectionSchema = z.object({
  status: ConnectionStatusSchema,
  id: z.string().uuid(),
  senderId: z.string(),
  receiverId: z.string(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type Connection = z.infer<typeof ConnectionSchema>

// CONNECTION OPTIONAL DEFAULTS SCHEMA
//------------------------------------------------------

export const ConnectionOptionalDefaultsSchema = ConnectionSchema.merge(z.object({
  status: ConnectionStatusSchema.optional(),
  id: z.string().uuid().optional(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
}))

export type ConnectionOptionalDefaults = z.infer<typeof ConnectionOptionalDefaultsSchema>

/////////////////////////////////////////
// MESSAGE SCHEMA
/////////////////////////////////////////

export const MessageSchema = z.object({
  id: z.string().uuid(),
  senderId: z.string(),
  receiverId: z.string(),
  content: z.string(),
  isRead: z.boolean(),
  readAt: z.coerce.date().nullish(),
  createdAt: z.coerce.date(),
})

export type Message = z.infer<typeof MessageSchema>

// MESSAGE OPTIONAL DEFAULTS SCHEMA
//------------------------------------------------------

export const MessageOptionalDefaultsSchema = MessageSchema.merge(z.object({
  id: z.string().uuid().optional(),
  isRead: z.boolean().optional(),
  createdAt: z.coerce.date().optional(),
}))

export type MessageOptionalDefaults = z.infer<typeof MessageOptionalDefaultsSchema>

/////////////////////////////////////////
// POST SCHEMA
/////////////////////////////////////////

export const PostSchema = z.object({
  type: PostTypeSchema,
  id: z.string().uuid(),
  authorId: z.string(),
  title: z.string().nullish(),
  content: z.string(),
  imageUrl: z.string().nullish(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type Post = z.infer<typeof PostSchema>

// POST OPTIONAL DEFAULTS SCHEMA
//------------------------------------------------------

export const PostOptionalDefaultsSchema = PostSchema.merge(z.object({
  type: PostTypeSchema.optional(),
  id: z.string().uuid().optional(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
}))

export type PostOptionalDefaults = z.infer<typeof PostOptionalDefaultsSchema>

/////////////////////////////////////////
// COMMENT SCHEMA
/////////////////////////////////////////

export const CommentSchema = z.object({
  id: z.string().uuid(),
  postId: z.string(),
  authorId: z.string(),
  content: z.string(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type Comment = z.infer<typeof CommentSchema>

// COMMENT OPTIONAL DEFAULTS SCHEMA
//------------------------------------------------------

export const CommentOptionalDefaultsSchema = CommentSchema.merge(z.object({
  id: z.string().uuid().optional(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
}))

export type CommentOptionalDefaults = z.infer<typeof CommentOptionalDefaultsSchema>

/////////////////////////////////////////
// POST LIKE SCHEMA
/////////////////////////////////////////

export const PostLikeSchema = z.object({
  id: z.string().uuid(),
  postId: z.string(),
  userId: z.string(),
  createdAt: z.coerce.date(),
})

export type PostLike = z.infer<typeof PostLikeSchema>

// POST LIKE OPTIONAL DEFAULTS SCHEMA
//------------------------------------------------------

export const PostLikeOptionalDefaultsSchema = PostLikeSchema.merge(z.object({
  id: z.string().uuid().optional(),
  createdAt: z.coerce.date().optional(),
}))

export type PostLikeOptionalDefaults = z.infer<typeof PostLikeOptionalDefaultsSchema>

/////////////////////////////////////////
// EVENT SCHEMA
/////////////////////////////////////////

export const EventSchema = z.object({
  type: EventTypeSchema,
  id: z.string().uuid(),
  title: z.string(),
  description: z.string(),
  startDateTime: z.coerce.date(),
  endDateTime: z.coerce.date(),
  location: z.string().nullish(),
  isVirtual: z.boolean(),
  virtualUrl: z.string().nullish(),
  maxAttendees: z.number().int().nullish(),
  imageUrl: z.string().nullish(),
  organizerInfo: z.string().nullish(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type Event = z.infer<typeof EventSchema>

// EVENT OPTIONAL DEFAULTS SCHEMA
//------------------------------------------------------

export const EventOptionalDefaultsSchema = EventSchema.merge(z.object({
  id: z.string().uuid().optional(),
  isVirtual: z.boolean().optional(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
}))

export type EventOptionalDefaults = z.infer<typeof EventOptionalDefaultsSchema>

/////////////////////////////////////////
// EVENT REGISTRATION SCHEMA
/////////////////////////////////////////

export const EventRegistrationSchema = z.object({
  status: RegistrationStatusSchema,
  id: z.string().uuid(),
  eventId: z.string(),
  userId: z.string(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type EventRegistration = z.infer<typeof EventRegistrationSchema>

// EVENT REGISTRATION OPTIONAL DEFAULTS SCHEMA
//------------------------------------------------------

export const EventRegistrationOptionalDefaultsSchema = EventRegistrationSchema.merge(z.object({
  status: RegistrationStatusSchema.optional(),
  id: z.string().uuid().optional(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
}))

export type EventRegistrationOptionalDefaults = z.infer<typeof EventRegistrationOptionalDefaultsSchema>

/////////////////////////////////////////
// NEWS ARTICLE SCHEMA
/////////////////////////////////////////

export const NewsArticleSchema = z.object({
  category: NewsCategorySchema,
  id: z.string().uuid(),
  title: z.string(),
  content: z.string(),
  excerpt: z.string().nullish(),
  imageUrl: z.string().nullish(),
  published: z.boolean(),
  publishedAt: z.coerce.date().nullish(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type NewsArticle = z.infer<typeof NewsArticleSchema>

// NEWS ARTICLE OPTIONAL DEFAULTS SCHEMA
//------------------------------------------------------

export const NewsArticleOptionalDefaultsSchema = NewsArticleSchema.merge(z.object({
  category: NewsCategorySchema.optional(),
  id: z.string().uuid().optional(),
  published: z.boolean().optional(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
}))

export type NewsArticleOptionalDefaults = z.infer<typeof NewsArticleOptionalDefaultsSchema>

/////////////////////////////////////////
// DONATION SCHEMA
/////////////////////////////////////////

export const DonationSchema = z.object({
  type: DonationTypeSchema,
  paymentStatus: PaymentStatusSchema,
  id: z.string().uuid(),
  donorId: z.string(),
  amount: z.instanceof(Prisma.Decimal, { message: "Field 'amount' must be a Decimal. Location: ['Models', 'Donation']"}),
  currency: z.string(),
  purpose: z.string().nullish(),
  isAnonymous: z.boolean(),
  paymentMethod: z.string().nullish(),
  transactionId: z.string().nullish(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type Donation = z.infer<typeof DonationSchema>

// DONATION OPTIONAL DEFAULTS SCHEMA
//------------------------------------------------------

export const DonationOptionalDefaultsSchema = DonationSchema.merge(z.object({
  type: DonationTypeSchema.optional(),
  paymentStatus: PaymentStatusSchema.optional(),
  id: z.string().uuid().optional(),
  currency: z.string().optional(),
  isAnonymous: z.boolean().optional(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
}))

export type DonationOptionalDefaults = z.infer<typeof DonationOptionalDefaultsSchema>
