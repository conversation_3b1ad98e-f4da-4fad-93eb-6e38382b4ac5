import React from "react";
import { type LucideIcon } from "lucide-react";
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "../ui/sidebar";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import { Badge } from "../ui/badge";

const NavMenu = ({
  items,
  title,
}: {
  items: {
    name: string;
    url: string;
    icon: LucideIcon | any;
    process?: boolean;
    new?: boolean;
  }[];
  title: string;
}) => {
  const pathname = usePathname();
  return (
    <SidebarGroup className="group-data-[collapsible=icon]:hidden">
      <SidebarGroupLabel className="dark: text-gray-500 mb-1.5 text-sm">
        {title}
      </SidebarGroupLabel>
      <SidebarMenu>
        {items.map((item) => {
          const isActive = pathname.startsWith(item.url);
          return (
            <SidebarMenuItem
              key={item.name}
              className={cn(
                isActive &&
                  "rounded-md border-r-4 bg-linear-to-r from-transparent to-primary/20 border-r-primary"
              )}
            >
              <SidebarMenuButton
                className="hover:bg-linear-to-r hover:from-transparent hover:to-primary/20"
                asChild
              >
                <Link
                  href={item.url}
                  className="gap-4 w-full flex items-center "
                >
                  <item.icon
                    className={cn(
                      "dark:text-gray-400 text-gray-700",
                      isActive && ""
                    )}
                  />
                  <span className={cn("font-medium")}>{item.name}</span>

                  {item.process && (
                    <Badge className="px-1 justify-end ml-auto text-right py-0.5 text-xs">
                      Coming Soon
                    </Badge>
                  )}
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          );
        })}
      </SidebarMenu>
    </SidebarGroup>
  );
};

export default NavMenu;
