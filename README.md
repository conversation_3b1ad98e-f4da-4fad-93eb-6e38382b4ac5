# PROTEC Alumni Platform

A comprehensive digital platform connecting PROTEC alumni, fostering networking, engagement, and community building. The Programme for Technology Careers (PROTEC) is a South African non-profit organization dedicated to empowering youth from disadvantaged backgrounds through STEM education and career guidance.

## 🎯 Mission

Since 1982, PROTEC has supported over 40,000 learners in their journeys toward successful careers in technology and engineering. This platform serves as a central hub for the extensive PROTEC alumni network, strengthening community bonds and amplifying the organization's impact.

## ✨ Key Features

### 🤝 Networking & Connections
- **Alumni Directory**: Searchable directory with filters for graduation year, industry, location, and skills
- **Connection Management**: Send/accept connection requests with secure messaging
- **Mentorship Matching**: Connect mentors with mentees based on skills and career paths
- **Professional Networking**: Industry-based networking and career opportunities

### 📅 Events & Calendar
- **Event Management**: Workshops, conferences, webinars, networking events, and fundraisers
- **Registration System**: RSVP functionality with capacity management
- **Event Types**: Virtual and physical events with detailed descriptions
- **Attendance Tracking**: Registration states and attendance confirmation

### 💰 Donation Portal
- **Secure Payments**: Integration with PayFast, PayPal, and credit cards
- **Donation Types**: One-time, monthly, quarterly, and annual contributions
- **Impact Tracking**: Donation history and impact reporting
- **Anonymous Options**: Privacy controls for donors

### 📰 News Feed & Content
- **Alumni Posts**: Share success stories, job opportunities, and general updates
- **Official News**: Organizational updates, announcements, and featured content
- **Engagement**: Like, comment, and share functionality
- **Content Categories**: Success stories, job opportunities, mentorship offers

### 🏢 Organization Management
- **Multi-Organization Support**: PROTEC and partner organization management
- **Role-Based Access**: Alumni, moderators, administrators, and super admin roles
- **Invitation System**: Email-based invitations with role pre-assignment
- **Organizational Branding**: Custom logos and metadata

### 🔔 Notification System
- **Real-time Alerts**: Connection requests, messages, event reminders
- **Personalized Preferences**: Customizable notification settings
- **Deep Linking**: Direct navigation to relevant content
- **Organization-Scoped**: Role-based notification targeting

## 🛠 Technology Stack

### Frontend
- **Web Application**: Next.js 15.3 with React 19 and App Router
- **Mobile Application**: React Native 0.79 with Expo 53
- **UI Framework**: TailwindCSS v4 with shadcn/ui components
- **State Management**: TanStack Query for data fetching and caching
- **Form Management**: TanStack Form for form handling

### Backend
- **API Server**: Hono lightweight web framework
- **Type Safety**: oRPC for end-to-end type-safe APIs with OpenAPI
- **Runtime**: Node.js with TypeScript
- **Authentication**: Better Auth with organization support and RBAC

### Database
- **Primary Database**: MySQL with Prisma ORM
- **Schema Management**: Prisma migrations and generated client
- **Multi-Organization**: Organization, Member, and Invitation models

### Development Tools
- **Monorepo**: Turborepo for optimized build system and caching
- **Package Manager**: pnpm for efficient dependency management
- **Code Quality**: Biome for linting, formatting, and organization
- **Development Server**: tsx for TypeScript execution

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ 
- pnpm (recommended) or npm
- MySQL database

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd protec-alumni
   ```

2. **Install dependencies**
   ```bash
   pnpm install
   ```

3. **Environment Setup**
   ```bash
   cp .env.example .env
   # Configure your environment variables
   ```

4. **Database Setup**
   ```bash
   pnpm db:push          # Push schema changes
   pnpm db:generate      # Generate Prisma client
   pnpm db:migrate       # Run migrations
   ```

5. **Start Development**
   ```bash
   pnpm dev              # Start all applications
   pnpm dev:web          # Web application only
   pnpm dev:native       # Mobile application only
   pnpm dev:server       # API server only
   ```

6. **Open Applications**
   - Web: [http://localhost:3000](http://localhost:3000)
   - API: [http://localhost:8000](http://localhost:8000)
   - Database Studio: `pnpm db:studio`

## 📁 Project Structure

```
protec-alumni/
├── apps/
│   ├── web/          # Next.js web application
│   ├── native/       # React Native mobile app
│   └── server/       # Hono API server
├── packages/         # Shared utilities (if needed)
├── prisma/          # Database schema and migrations
├── turbo.json       # Turborepo configuration
└── package.json     # Root package configuration
```

## 🔧 Development Commands

```bash
# Development
pnpm dev              # Start all applications
pnpm dev:web          # Web application only
pnpm dev:native       # Mobile application only
pnpm dev:server       # API server only

# Database Operations
pnpm db:push          # Push schema changes
pnpm db:generate      # Generate Prisma client
pnpm db:migrate       # Run database migrations
pnpm db:studio        # Open database management UI

# Quality Assurance
pnpm build            # Build all applications
pnpm check-types      # TypeScript validation
pnpm check            # Code formatting and linting
```

## 🏗 Database Schema

### Core Models
- **User**: Authentication and basic user information
- **AlumniProfile**: Extended profile with PROTEC history and career info
- **Organization**: Multi-organization support with branding
- **Member**: User-organization relationships with roles
- **Connection**: Alumni networking and relationship management
- **Event**: Event management with registration capabilities
- **Post**: User-generated content and news feed
- **Donation**: Contribution tracking and payment management
- **Notification**: Real-time communication system

## 🔐 Security Features

- **Authentication**: Better Auth with secure session management
- **API Security**: Type-safe APIs with oRPC validation
- **Data Protection**: GDPR and POPIA compliance
- **Database Security**: Prisma ORM with parameterized queries
- **Environment Security**: Secure environment variable management
- **Regular Audits**: Penetration testing and code reviews

## 📱 Platform Support

- **Android**: Google Play Store distribution
- **iOS**: Apple App Store distribution
- **Huawei**: App Gallery availability
- **Web**: Progressive Web App (PWA) capabilities
- **Desktop**: Cross-platform web access

## 📊 Success Metrics

- **User Adoption**: Active users across platforms
- **Engagement**: Session duration and feature utilization
- **Event Participation**: RSVP and attendance rates
- **Donations**: Volume and frequency of contributions
- **Community Growth**: Network connections and interactions
- **Technical Performance**: Load times and reliability

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines
- Follow TypeScript best practices
- Use Biome for code formatting and linting
- Write tests for new features
- Update documentation as needed
- Follow the established project structure

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- PROTEC organization for their mission and vision
- Alumni community for their continued engagement
- Open source contributors and maintainers
- Technology partners and sponsors

## 📞 Support

For support, email <EMAIL> or join our community discussions.

---

**Built with ❤️ for the PROTEC Alumni Community**
