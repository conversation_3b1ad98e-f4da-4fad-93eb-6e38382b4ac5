import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { FiClock, FiFlag } from "react-icons/fi";

export interface Project {
  name: string;
  status: "active" | "completed" | "paused";
  progress: number;
  dueDate: string;
  team: string[];
  priority?: "low" | "medium" | "high";
  lastUpdated?: string;
}

interface ProjectTableProps {
  projects: Project[];
}

export const ProjectTable = ({ projects }: ProjectTableProps) => {
  const getStatusColor = (status: Project["status"]) => {
    switch (status) {
      case "active":
        return "bg-green-500/10 text-green-500";
      case "completed":
        return "bg-blue-500/10 text-blue-500";
      case "paused":
        return "bg-orange-500/10 text-orange-500";
    }
  };

  const getPriorityColor = (priority: Project["priority"]) => {
    switch (priority) {
      case "high":
        return "text-red-500";
      case "medium":
        return "text-yellow-500";
      case "low":
        return "text-green-500";
      default:
        return "text-gray-500";
    }
  };

  const getProgressColor = (progress: number) => {
    if (progress >= 75) return "bg-green-500";
    if (progress >= 50) return "bg-yellow-500";
    return "bg-orange-500";
  };

  return (
    <div className="rounded-lg border p-4 bg-card">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>#</TableHead>
            <TableHead className="w-[250px]">Project</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Priority</TableHead>
            <TableHead>Progress</TableHead>
            <TableHead>Due Date</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {projects.map((project, index) => (
            <TableRow key={index} className="group hover:bg-muted/50">
              <TableCell>
                <Badge>{index + 1}</Badge>
              </TableCell>
              <TableCell className="font-medium">
                <div className="flex flex-col">
                  <span>{project.name}</span>
                  <span className="text-xs text-muted-foreground mt-1">
                    ID: PRJ-{(index + 1).toString().padStart(4, "0")}
                  </span>
                </div>
              </TableCell>
              <TableCell>
                <Badge className={getStatusColor(project.status)}>
                  {project.status}
                </Badge>
              </TableCell>
              <TableCell>
                <div className="flex items-center gap-2">
                  <FiFlag className={getPriorityColor(project.priority)} />
                  <span className="text-sm capitalize">
                    {project.priority || "none"}
                  </span>
                </div>
              </TableCell>
              <TableCell>
                <div className="flex flex-col gap-2 w-[150px]">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">
                      {project.progress}%
                    </span>
                    <span className="text-xs text-muted-foreground">
                      {project.progress === 100 ? "Completed" : "In Progress"}
                    </span>
                  </div>
                  <div className="h-2 rounded-full bg-secondary">
                    <div
                      className={`h-full rounded-full transition-all ${getProgressColor(
                        project.progress
                      )}`}
                      style={{ width: `${project.progress}%` }}
                    />
                  </div>
                </div>
              </TableCell>
              <TableCell>
                <div className="flex items-center gap-2">
                  <FiClock className="text-muted-foreground" />
                  <span>{project.dueDate}</span>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};
