
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('./runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.14.0
 * Query Engine version: 717184b7b35ea05dfa71a3236b7af656013e1e49
 */
Prisma.prismaVersion = {
  client: "6.14.0",
  engine: "717184b7b35ea05dfa71a3236b7af656013e1e49"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  name: 'name',
  email: 'email',
  emailVerified: 'emailVerified',
  image: 'image',
  role: 'role',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SessionScalarFieldEnum = {
  id: 'id',
  expiresAt: 'expiresAt',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  userId: 'userId',
  activeOrganizationId: 'activeOrganizationId',
  token: 'token',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AccountScalarFieldEnum = {
  id: 'id',
  accountId: 'accountId',
  providerId: 'providerId',
  userId: 'userId',
  accessToken: 'accessToken',
  refreshToken: 'refreshToken',
  idToken: 'idToken',
  expiresAt: 'expiresAt',
  password: 'password',
  accessTokenExpiresAt: 'accessTokenExpiresAt',
  refreshTokenExpiresAt: 'refreshTokenExpiresAt',
  scope: 'scope',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.VerificationScalarFieldEnum = {
  id: 'id',
  identifier: 'identifier',
  value: 'value',
  expiresAt: 'expiresAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.OrganizationScalarFieldEnum = {
  id: 'id',
  name: 'name',
  slug: 'slug',
  logo: 'logo',
  createdAt: 'createdAt',
  metadata: 'metadata'
};

exports.Prisma.MemberScalarFieldEnum = {
  id: 'id',
  organizationId: 'organizationId',
  userId: 'userId',
  role: 'role',
  createdAt: 'createdAt'
};

exports.Prisma.InvitationScalarFieldEnum = {
  id: 'id',
  organizationId: 'organizationId',
  email: 'email',
  role: 'role',
  status: 'status',
  expiresAt: 'expiresAt',
  inviterId: 'inviterId'
};

exports.Prisma.PasskeyScalarFieldEnum = {
  id: 'id',
  name: 'name',
  publicKey: 'publicKey',
  userId: 'userId',
  webauthnUserID: 'webauthnUserID',
  counter: 'counter',
  deviceType: 'deviceType',
  backedUp: 'backedUp',
  transports: 'transports',
  createdAt: 'createdAt',
  credentialID: 'credentialID'
};

exports.Prisma.TwoFactorScalarFieldEnum = {
  id: 'id',
  secret: 'secret',
  backupCodes: 'backupCodes',
  userId: 'userId'
};

exports.Prisma.NotificationScalarFieldEnum = {
  id: 'id',
  title: 'title',
  message: 'message',
  type: 'type',
  priority: 'priority',
  created_at: 'created_at',
  userId: 'userId',
  read: 'read',
  readAt: 'readAt',
  actionUrl: 'actionUrl'
};

exports.Prisma.NotificationSettingsScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  inApp: 'inApp',
  email: 'email',
  systemUpdates: 'systemUpdates',
  newFeature: 'newFeature',
  securityAlert: 'securityAlert',
  message: 'message',
  promotion: 'promotion',
  reminder: 'reminder'
};

exports.Prisma.AlumniProfileScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  firstName: 'firstName',
  lastName: 'lastName',
  bio: 'bio',
  profilePicture: 'profilePicture',
  graduationYear: 'graduationYear',
  programType: 'programType',
  centerLocation: 'centerLocation',
  currentPosition: 'currentPosition',
  currentCompany: 'currentCompany',
  industry: 'industry',
  workLocation: 'workLocation',
  linkedinUrl: 'linkedinUrl',
  phoneNumber: 'phoneNumber',
  profileVisibility: 'profileVisibility',
  emailVisible: 'emailVisible',
  phoneVisible: 'phoneVisible',
  locationVisible: 'locationVisible',
  offeringMentorship: 'offeringMentorship',
  seekingMentorship: 'seekingMentorship',
  skillsOffered: 'skillsOffered',
  skillsWanted: 'skillsWanted',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ConnectionScalarFieldEnum = {
  id: 'id',
  senderId: 'senderId',
  receiverId: 'receiverId',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.MessageScalarFieldEnum = {
  id: 'id',
  senderId: 'senderId',
  receiverId: 'receiverId',
  content: 'content',
  isRead: 'isRead',
  readAt: 'readAt',
  createdAt: 'createdAt'
};

exports.Prisma.PostScalarFieldEnum = {
  id: 'id',
  authorId: 'authorId',
  title: 'title',
  content: 'content',
  type: 'type',
  imageUrl: 'imageUrl',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CommentScalarFieldEnum = {
  id: 'id',
  postId: 'postId',
  authorId: 'authorId',
  content: 'content',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PostLikeScalarFieldEnum = {
  id: 'id',
  postId: 'postId',
  userId: 'userId',
  createdAt: 'createdAt'
};

exports.Prisma.EventScalarFieldEnum = {
  id: 'id',
  title: 'title',
  description: 'description',
  type: 'type',
  startDateTime: 'startDateTime',
  endDateTime: 'endDateTime',
  location: 'location',
  isVirtual: 'isVirtual',
  virtualUrl: 'virtualUrl',
  maxAttendees: 'maxAttendees',
  imageUrl: 'imageUrl',
  organizerInfo: 'organizerInfo',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EventRegistrationScalarFieldEnum = {
  id: 'id',
  eventId: 'eventId',
  userId: 'userId',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.NewsArticleScalarFieldEnum = {
  id: 'id',
  title: 'title',
  content: 'content',
  excerpt: 'excerpt',
  category: 'category',
  imageUrl: 'imageUrl',
  published: 'published',
  publishedAt: 'publishedAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.DonationScalarFieldEnum = {
  id: 'id',
  donorId: 'donorId',
  amount: 'amount',
  currency: 'currency',
  type: 'type',
  purpose: 'purpose',
  isAnonymous: 'isAnonymous',
  paymentMethod: 'paymentMethod',
  transactionId: 'transactionId',
  paymentStatus: 'paymentStatus',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.UserOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  email: 'email',
  image: 'image',
  role: 'role'
};

exports.Prisma.SessionOrderByRelevanceFieldEnum = {
  id: 'id',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  userId: 'userId',
  activeOrganizationId: 'activeOrganizationId',
  token: 'token'
};

exports.Prisma.AccountOrderByRelevanceFieldEnum = {
  id: 'id',
  accountId: 'accountId',
  providerId: 'providerId',
  userId: 'userId',
  accessToken: 'accessToken',
  refreshToken: 'refreshToken',
  idToken: 'idToken',
  password: 'password',
  scope: 'scope'
};

exports.Prisma.VerificationOrderByRelevanceFieldEnum = {
  id: 'id',
  identifier: 'identifier',
  value: 'value'
};

exports.Prisma.OrganizationOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  slug: 'slug',
  logo: 'logo',
  metadata: 'metadata'
};

exports.Prisma.MemberOrderByRelevanceFieldEnum = {
  id: 'id',
  organizationId: 'organizationId',
  userId: 'userId',
  role: 'role'
};

exports.Prisma.InvitationOrderByRelevanceFieldEnum = {
  id: 'id',
  organizationId: 'organizationId',
  email: 'email',
  role: 'role',
  status: 'status',
  inviterId: 'inviterId'
};

exports.Prisma.PasskeyOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  publicKey: 'publicKey',
  userId: 'userId',
  webauthnUserID: 'webauthnUserID',
  deviceType: 'deviceType',
  transports: 'transports',
  credentialID: 'credentialID'
};

exports.Prisma.TwoFactorOrderByRelevanceFieldEnum = {
  id: 'id',
  secret: 'secret',
  backupCodes: 'backupCodes',
  userId: 'userId'
};

exports.Prisma.NotificationOrderByRelevanceFieldEnum = {
  id: 'id',
  title: 'title',
  message: 'message',
  userId: 'userId',
  actionUrl: 'actionUrl'
};

exports.Prisma.NotificationSettingsOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId'
};

exports.Prisma.AlumniProfileOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  firstName: 'firstName',
  lastName: 'lastName',
  bio: 'bio',
  profilePicture: 'profilePicture',
  centerLocation: 'centerLocation',
  currentPosition: 'currentPosition',
  currentCompany: 'currentCompany',
  industry: 'industry',
  workLocation: 'workLocation',
  linkedinUrl: 'linkedinUrl',
  phoneNumber: 'phoneNumber',
  skillsOffered: 'skillsOffered',
  skillsWanted: 'skillsWanted'
};

exports.Prisma.ConnectionOrderByRelevanceFieldEnum = {
  id: 'id',
  senderId: 'senderId',
  receiverId: 'receiverId'
};

exports.Prisma.MessageOrderByRelevanceFieldEnum = {
  id: 'id',
  senderId: 'senderId',
  receiverId: 'receiverId',
  content: 'content'
};

exports.Prisma.PostOrderByRelevanceFieldEnum = {
  id: 'id',
  authorId: 'authorId',
  title: 'title',
  content: 'content',
  imageUrl: 'imageUrl'
};

exports.Prisma.CommentOrderByRelevanceFieldEnum = {
  id: 'id',
  postId: 'postId',
  authorId: 'authorId',
  content: 'content'
};

exports.Prisma.PostLikeOrderByRelevanceFieldEnum = {
  id: 'id',
  postId: 'postId',
  userId: 'userId'
};

exports.Prisma.EventOrderByRelevanceFieldEnum = {
  id: 'id',
  title: 'title',
  description: 'description',
  location: 'location',
  virtualUrl: 'virtualUrl',
  imageUrl: 'imageUrl',
  organizerInfo: 'organizerInfo'
};

exports.Prisma.EventRegistrationOrderByRelevanceFieldEnum = {
  id: 'id',
  eventId: 'eventId',
  userId: 'userId'
};

exports.Prisma.NewsArticleOrderByRelevanceFieldEnum = {
  id: 'id',
  title: 'title',
  content: 'content',
  excerpt: 'excerpt',
  imageUrl: 'imageUrl'
};

exports.Prisma.DonationOrderByRelevanceFieldEnum = {
  id: 'id',
  donorId: 'donorId',
  currency: 'currency',
  purpose: 'purpose',
  paymentMethod: 'paymentMethod',
  transactionId: 'transactionId'
};
exports.notification_type = exports.$Enums.notification_type = {
  account: 'account',
  system_update: 'system_update',
  new_feature: 'new_feature',
  security_alert: 'security_alert',
  message: 'message',
  promotion: 'promotion',
  reminder: 'reminder'
};

exports.notification_priority = exports.$Enums.notification_priority = {
  low: 'low',
  medium: 'medium',
  high: 'high',
  urgent: 'urgent'
};

exports.ProgramType = exports.$Enums.ProgramType = {
  ENGINEERING: 'ENGINEERING',
  BUSINESS: 'BUSINESS',
  SCIENCE: 'SCIENCE',
  TECHNOLOGY: 'TECHNOLOGY',
  OTHER: 'OTHER'
};

exports.ProfileVisibility = exports.$Enums.ProfileVisibility = {
  PUBLIC: 'PUBLIC',
  ALUMNI_ONLY: 'ALUMNI_ONLY',
  PRIVATE: 'PRIVATE'
};

exports.ConnectionStatus = exports.$Enums.ConnectionStatus = {
  PENDING: 'PENDING',
  ACCEPTED: 'ACCEPTED',
  DECLINED: 'DECLINED',
  BLOCKED: 'BLOCKED'
};

exports.PostType = exports.$Enums.PostType = {
  GENERAL: 'GENERAL',
  SUCCESS_STORY: 'SUCCESS_STORY',
  JOB_OPPORTUNITY: 'JOB_OPPORTUNITY',
  MENTORSHIP: 'MENTORSHIP',
  ANNOUNCEMENT: 'ANNOUNCEMENT'
};

exports.EventType = exports.$Enums.EventType = {
  WORKSHOP: 'WORKSHOP',
  NETWORKING: 'NETWORKING',
  CONFERENCE: 'CONFERENCE',
  WEBINAR: 'WEBINAR',
  SOCIAL: 'SOCIAL',
  FUNDRAISING: 'FUNDRAISING',
  MENTORSHIP: 'MENTORSHIP'
};

exports.RegistrationStatus = exports.$Enums.RegistrationStatus = {
  REGISTERED: 'REGISTERED',
  ATTENDED: 'ATTENDED',
  NO_SHOW: 'NO_SHOW',
  CANCELLED: 'CANCELLED'
};

exports.NewsCategory = exports.$Enums.NewsCategory = {
  GENERAL: 'GENERAL',
  EVENTS: 'EVENTS',
  SUCCESS_STORIES: 'SUCCESS_STORIES',
  OPPORTUNITIES: 'OPPORTUNITIES',
  ANNOUNCEMENTS: 'ANNOUNCEMENTS'
};

exports.DonationType = exports.$Enums.DonationType = {
  ONE_TIME: 'ONE_TIME',
  MONTHLY: 'MONTHLY',
  QUARTERLY: 'QUARTERLY',
  ANNUAL: 'ANNUAL'
};

exports.PaymentStatus = exports.$Enums.PaymentStatus = {
  PENDING: 'PENDING',
  COMPLETED: 'COMPLETED',
  FAILED: 'FAILED',
  REFUNDED: 'REFUNDED',
  CANCELLED: 'CANCELLED'
};

exports.Prisma.ModelName = {
  User: 'User',
  Session: 'Session',
  Account: 'Account',
  Verification: 'Verification',
  Organization: 'Organization',
  Member: 'Member',
  Invitation: 'Invitation',
  Passkey: 'Passkey',
  TwoFactor: 'TwoFactor',
  Notification: 'Notification',
  NotificationSettings: 'NotificationSettings',
  AlumniProfile: 'AlumniProfile',
  Connection: 'Connection',
  Message: 'Message',
  Post: 'Post',
  Comment: 'Comment',
  PostLike: 'PostLike',
  Event: 'Event',
  EventRegistration: 'EventRegistration',
  NewsArticle: 'NewsArticle',
  Donation: 'Donation'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
