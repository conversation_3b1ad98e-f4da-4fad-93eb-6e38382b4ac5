"use client";

import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";
import {
  AlertCircle,
  AlertTriangle,
  ArrowRightIcon,
  CheckCircle2,
  Info,
  XCircle,
} from "lucide-react";

const alertVariants = cva(
  "relative w-full rounded-lg border px-4 py-3 text-sm",
  {
    variants: {
      variant: {
        default: "bg-card text-card-foreground",
        destructive:
          "text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90",
        success:
          "bg-emerald-50 text-emerald-600 border border-emerald-500/50 dark:bg-emerald-950/50 dark:border-emerald-800/50 dark:text-emerald-400",
        warning:
          "bg-amber-50 text-amber-600 border border-amber-500/50 dark:bg-amber-950/50 dark:border-amber-800/50 dark:text-amber-400",
        error:
          "bg-red-50 text-red-600 border border-red-500/50 dark:bg-red-950/50 dark:border-red-800/50 dark:text-red-400",
        info: "bg-sky-50 text-sky-600 border border-sky-500/50 dark:bg-sky-950/50 dark:border-sky-800/50 dark:text-sky-400",
        dark: "bg-gray-900 text-white border border-gray-800 dark:bg-gray-800 dark:border-gray-700",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
);

const iconMap = {
  default: Info,
  destructive: AlertCircle,
  success: CheckCircle2,
  warning: AlertTriangle,
  error: XCircle,
  info: AlertCircle,
  dark: Info,
};

export interface AlertProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof alertVariants> {
  /**
   * Whether to show an icon
   * @default false
   */
  showIcon?: boolean;
  /**
   * Custom icon to display
   */
  icon?: React.ReactNode;
  /**
   * Action button or link
   */
  action?: React.ReactNode;
  /**
   * Action text if using default action
   */
  actionText?: string;
  /**
   * Action href if using default action
   */
  actionHref?: string;
  /**
   * Action click handler if using default action
   */
  onActionClick?: () => void;
}

const Alert = React.forwardRef<HTMLDivElement, AlertProps>(
  (
    {
      className,
      variant = "default",
      showIcon = false,
      icon,
      action,
      actionText,
      actionHref,
      onActionClick,
      children,
      ...props
    },
    ref
  ) => {
    // Determine which icon to show
    const IconComponent = iconMap[variant as keyof typeof iconMap] || Info;

    // Create default action if actionText is provided
    const defaultAction = actionText ? (
      <a
        href={actionHref || "#"}
        onClick={(e) => {
          if (!actionHref && onActionClick) {
            e.preventDefault();
            onActionClick();
          }
        }}
        className="group text-sm font-medium whitespace-nowrap"
      >
        {actionText}
        <ArrowRightIcon
          className="ms-1 -mt-0.5 inline-flex opacity-60 transition-transform group-hover:translate-x-0.5"
          size={16}
          aria-hidden="true"
        />
      </a>
    ) : null;

    // Check if children contain AlertTitle or AlertDescription components
    const hasAlertComponents = React.Children.toArray(children).some(
      (child) =>
        React.isValidElement(child) &&
        (child.type === AlertTitle || child.type === AlertDescription)
    );

    // Check if there are any icon elements (SVG or Lucide icons)
    const hasIconElements = React.Children.toArray(children).some(
      (child) =>
        React.isValidElement(child) &&
        child.props &&
        typeof child.props === "object" &&
        "className" in child.props &&
        typeof child.props.className === "string" &&
        (child.props.className.includes("h-4 w-4") ||
          child.props.className.includes("h-5 w-5"))
    );

    // For backward compatibility, if no special features are used, render the simple version
    if (
      !showIcon &&
      !icon &&
      !action &&
      !actionText &&
      !actionHref &&
      !onActionClick &&
      !hasAlertComponents
    ) {
      return (
        <div
          ref={ref}
          data-slot="alert"
          role="alert"
          className={cn(
            alertVariants({ variant }),
            "grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",
            className
          )}
          {...props}
        >
          {children}
        </div>
      );
    }

    // If we have AlertTitle or AlertDescription components, use the grid layout
    if (hasAlertComponents || hasIconElements) {
      // Extract action elements to place them at the end
      const actionElement = action || defaultAction;

      return (
        <div
          ref={ref}
          data-slot="alert"
          role="alert"
          className={cn(
            alertVariants({ variant }),
            "grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",
            className
          )}
          {...props}
        >
          {/* Add the default icon if showIcon is true and no icon is provided in children */}
          {showIcon && !hasIconElements && (
            <IconComponent
              className="size-4 text-current opacity-70"
              aria-hidden="true"
            />
          )}
          {children}
          {(action || defaultAction) && (
            <div className="col-start-2 flex justify-end mt-2">
              {actionElement}
            </div>
          )}
        </div>
      );
    }

    // Enhanced version with icon and/or action
    return (
      <div
        ref={ref}
        data-slot="alert"
        role="alert"
        className={cn(alertVariants({ variant }), className)}
        {...props}
      >
        <div className="flex gap-3">
          {showIcon &&
            (icon || (
              <IconComponent
                className="mt-0.5 shrink-0 opacity-60"
                size={16}
                aria-hidden="true"
              />
            ))}
          <div className="flex grow justify-between gap-3">
            <div className="text-sm">{children}</div>
            {action || defaultAction}
          </div>
        </div>
      </div>
    );
  }
);

Alert.displayName = "Alert";

function AlertTitle({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="alert-title"
      className={cn(
        "col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight",
        className
      )}
      {...props}
    />
  );
}

function AlertDescription({
  className,
  ...props
}: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="alert-description"
      className={cn(
        "text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",
        className
      )}
      {...props}
    />
  );
}

export { Alert, AlertTitle, AlertDescription, alertVariants };
