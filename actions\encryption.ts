"use server";
import crypto from "crypto";

// Function to encrypt data
export async function encryptData(data: string) {
  "use server";
  const secretKey = process.env.ENCRYPTION_SECRET_KEY!;
  const keyBuffer = Buffer.from(secretKey, "hex");

  const iv = crypto.randomBytes(16); // Initialization vector
  const cipher = crypto.createCipheriv("aes-256-cbc", keyBuffer, iv);
  let encrypted = cipher.update(data, "utf8", "hex");
  encrypted += cipher.final("hex");
  return { iv: iv.toString("hex"), encrypted };
}

// Function to decrypt data
export async function decryptData({ key, iv }: { key: string; iv: string }) {
  "use server";
  const secretKey = process.env.ENCRYPTION_SECRET_KEY!;
  const keyBuffer = Buffer.from(secretKey, "hex");
  const ivBuffer = Buffer.from(iv, "hex");

  const decipher = crypto.createDecipheriv("aes-256-cbc", keyBuffer, ivBuffer);
  let decrypted = decipher.update(key, "hex", "utf8");
  decrypted += decipher.final("utf8");
  return decrypted;
}
