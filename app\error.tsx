"use client";
import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { Alert<PERSON>ircle, ArrowLeft, RefreshCcw } from "lucide-react";

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  const router = useRouter();

  useEffect(() => {
    // Log the error to an error reporting service
    console.error(error);
  }, [error]);

  return (
    <div className="min-h-screen flex items-center justify-center px-4 py-8">
      <div className="max-w-md w-full space-y-8 bg-card p-10 rounded-xl shadow-lg">
        <div className="flex flex-col items-center space-y-4">
          <AlertCircle className="h-16 w-16 text-red-500" />
          <h1 className="text-3xl font-bold text-center">
            Oops! Something went wrong
          </h1>
          <p className="text-center ">
            We apologize for the inconvenience. An unexpected error has
            occurred.
          </p>
          {error.digest && <p className="text-sm ">Error ID: {error.digest}</p>}
        </div>
        <div className="flex flex-col space-y-4">
          <button
            onClick={() => reset()}
            className="w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-xs text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <RefreshCcw className="h-5 w-5 mr-2" />
            Try again
          </button>
          <button
            onClick={() => router.back()}
            className="w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-xs text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <ArrowLeft className="h-5 w-5 mr-2" />
            Go back
          </button>
        </div>
      </div>
    </div>
  );
}
