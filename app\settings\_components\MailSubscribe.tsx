import { getSession } from "@/actions/account/user";
import { loops } from "@/lib/loops";
import React from "react";
import {
  Card,
  CardContent,
  CardDescription,
  <PERSON><PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Mail } from "lucide-react";
import HandleMailSubscribe from "./HandleMailSubscribe";

const MailSubscribe = async () => {
  const { email } = await getSession();

  const getContact = async () => {
    try {
      const account = await loops.findContact({
        email,
      });

      return account;
    } catch (error) {
      return null;
    }
  };

  const contact = await getContact();

  return (
    <Card>
      <CardHeader>
        <CardTitle>
          {contact
            ? "Unsubscribe from product updates emails"
            : "Subscribe to product updates emails"}
        </CardTitle>
        <CardDescription>
          We occasionally update our users on new features and changes via
          email.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="w-full border rounded-md p-3">
          <div className="flex w-full gap-4 justify-between">
            <div>
              <AlertTitle>{contact ? "Unsubscribe" : "Subscribe"}</AlertTitle>
              <AlertDescription>
                {contact ? "Stop receiving" : "Sign up for"} product updates
              </AlertDescription>
            </div>
            <HandleMailSubscribe active={contact ? true : false} />
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default MailSubscribe;
