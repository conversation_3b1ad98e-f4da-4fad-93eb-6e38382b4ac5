"use client";
import { subscribeToUpdates } from "@/actions/account/user";
import { Button } from "@/components/ui/button";
import { Mail } from "lucide-react";
import React, { useState } from "react";
import { toast } from "react-toastify";

const HandleMailSubscribe = ({ active }: { active: boolean }) => {
  //
  const [loading, setLoading] = useState(false);

  async function handleMailUpdate() {
    try {
      setLoading(true);
      await subscribeToUpdates(active);
      toast.success("Subscribed to product updates");
    } catch (error) {
      toast.error("Failed up subscribe to product updates");
    } finally {
      setLoading(false);
    }
  }

  return (
    <Button onClick={handleMailUpdate} loading={loading}>
      <Mail />
      {active ? "Unsubscribe" : "Join"}
    </Button>
  );
};

export default HandleMailSubscribe;
