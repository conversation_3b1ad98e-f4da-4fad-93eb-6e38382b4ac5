import { LoopsClient } from "loops";
export const loops = new LoopsClient(process.env.LOOPS_API_KEY as string);

type eventTypes =
  | "auth"
  | "password"
  | "notification"
  | "system"
  | "2-factor"
  | "invitation";
type Event = {
  id: eventTypes;
  eventName?: string;
  transactionName?: string;
};

export const loopsEvents: Event[] = [
  {
    id: "auth",
    eventName: "",
  },
  {
    id: "system",
    eventName: "",
  },
  {
    id: "password",
    eventName: "",
  },
  {
    id: "2-factor",
    eventName: "",
  },
  {
    id: "invitation",
    eventName: "",
  },
  {
    id: "notification",
    eventName: "",
    transactionName: "",
  },
];
