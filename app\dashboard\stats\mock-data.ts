import type { Project } from "./project-table";

export const mockProjects: Project[] = [
  {
    name: "Website Redesign",
    status: "active",
    progress: 75,
    dueDate: "2024-03-30",
    team: ["<PERSON>", "<PERSON>", "<PERSON>"],
    priority: "high",
    lastUpdated: "2 hours ago",
  },
  {
    name: "Mobile App Development",
    status: "paused",
    progress: 45,
    dueDate: "2024-04-15",
    team: ["<PERSON>", "<PERSON>"],
    priority: "medium",
    lastUpdated: "1 day ago",
  },
  {
    name: "Marketing Campaign",
    status: "completed",
    progress: 100,
    dueDate: "2024-03-01",
    team: ["<PERSON>", "<PERSON>", "<PERSON>"],
    priority: "low",
    lastUpdated: "1 week ago",
  },
  {
    name: "Database Migration",
    status: "active",
    progress: 30,
    dueDate: "2024-05-01",
    team: ["<PERSON>", "<PERSON>"],
    priority: "high",
    lastUpdated: "3 hours ago",
  },
  {
    name: "Database Migration",
    status: "active",
    progress: 30,
    dueDate: "2024-05-01",
    team: ["<PERSON>", "<PERSON>"],
    priority: "high",
    lastUpdated: "3 hours ago",
  },
];

export const statsData = [
  {
    title: "Total Revenue",
    value: "$45,231",
    trend: {
      value: 12.5,
      isPositive: true,
      data: [30, 34, 35, 42, 39, 35, 41, 43, 40, 45],
    },
  },
  {
    title: "Active Users",
    value: "1,234",
    trend: {
      value: 8.2,
      isPositive: true,
      data: [120, 115, 130, 125, 135, 140, 138, 145, 142, 134],
    },
  },
  {
    title: "Pending Tasks",
    value: "23",
    trend: {
      value: 5.1,
      isPositive: false,
      data: [18, 20, 22, 25, 27, 28, 25, 23, 24, 23],
    },
  },
  {
    title: "Completed Projects",
    value: "12",
    trend: {
      value: 15.3,
      isPositive: true,
      data: [8, 7, 9, 8, 10, 9, 11, 10, 12, 12],
    },
  },
];
