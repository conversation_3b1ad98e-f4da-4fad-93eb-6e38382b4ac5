import type React from "react";
import { formatDistanceToNow } from "date-fns";
import {
  Check,
  AlertTriangle,
  Info,
  MessageSquare,
  Gift,
  BellRing,
  Settings2,
} from "lucide-react";
import {
  notification_priority,
  notification_type,
} from "@/prisma/generated/client";

type NotificationItemProps = {
  id: string;
  title: string;
  message: string;
  priority: notification_priority;
  createdAt: string;
  type: notification_type;
  read: boolean;
  actionUrl?: string;
  onMarkAsRead: (id: string) => void;
};

export const NotificationItem: React.FC<NotificationItemProps> = ({
  id,
  title,
  message,
  priority,
  createdAt,
  read,
  onMarkAsRead,
  type,
  actionUrl,
}) => {
  const priorityColors = {
    low: "bg-blue-100 text-blue-800",
    medium: "bg-yellow-100 text-yellow-800",
    high: "bg-orange-100 text-orange-800",
    urgent: "bg-red-100 text-red-800",
  };

  const typeIcons = {
    account: <Settings2 className="w-4 h-4 mr-2" />,
    system_update: <Info className="w-4 h-4 mr-2" />,
    new_feature: <Gift className="w-4 h-4 mr-2" />,
    security_alert: <AlertTriangle className="w-4 h-4 mr-2" />,
    message: <MessageSquare className="w-4 h-4 mr-2" />,
    promotion: <Gift className="w-4 h-4 mr-2" />,
    reminder: <BellRing className="w-4 h-4 mr-2" />,
  };

  const NotificationIcon = typeIcons[type] || <Info className="w-4 h-4 mr-2" />;

  const content = (
    <>
      <div className="flex justify-between items-start mb-1">
        <div className="flex items-center">
          {NotificationIcon}
          <h3 className="text-sm font-semibold line-clamp-2">{title}</h3>
        </div>
        <span
          className={`text-xs px-2 py-0.5 rounded-full ${priorityColors[priority]}`}
        >
          {priority}
        </span>
      </div>
      <p className="text-sm text-muted-foreground mb-2 ml-6 line-clamp-3">
        {message}
      </p>
      <div className="flex justify-between items-center text-xs text-gray-500 ml-6">
        <span>
          {formatDistanceToNow(new Date(createdAt), { addSuffix: true })}
        </span>
        {!read && (
          <button
            onClick={(e) => {
              e.preventDefault(); // Prevent navigation if it's a link
              e.stopPropagation(); // Stop event bubbling
              onMarkAsRead(id);
            }}
            className="flex items-center text-blue-600 hover:text-blue-800"
          >
            <Check className="w-4 h-4 mr-1" />
            Mark as read
          </button>
        )}
      </div>
    </>
  );

  const commonClasses =
    "p-3 border-b  hover:bg-muted transition-colors duration-150 ease-in-out";

  return actionUrl ? (
    <a
      href={actionUrl}
      target="_blank"
      rel="noopener noreferrer"
      className={`${commonClasses} block cursor-pointer`}
    >
      {content}
    </a>
  ) : (
    <div className={commonClasses}>{content}</div>
  );
};
